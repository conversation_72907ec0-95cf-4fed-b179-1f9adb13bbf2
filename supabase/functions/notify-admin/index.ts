import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface NotifyBody {
  event: "account_created" | "order_completed";
  email?: string; // for account_created
  session_id?: string; // for order_completed
  meta?: Record<string, unknown>;
}

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));
const TO_EMAIL = "<EMAIL>"; // Temporary until domain is verified
serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body: NotifyBody = await req.json();
    const origin = Deno.env.get("PUBLIC_SITE_URL") || req.headers.get("origin") || "https://pixicards.co";

    const subjectMap = {
      account_created: "🆕 New PixiCards account created",
      order_completed: "✅ New PixiCards order completed",
    } as const;

    let html = "";

    if (body.event === "account_created") {
      html = `
        <h2>New account created</h2>
        <p><strong>Email:</strong> ${body.email || "(unknown)"}</p>
        <p>View users in Supabase: <a href="${origin}">${origin}</a></p>
      `;
    } else if (body.event === "order_completed") {
      const sessionId = body.session_id || "(unknown)";
      html = `
        <h2>New order completed</h2>
        <p><strong>Stripe Checkout Session:</strong> ${sessionId}</p>
        <p>Check Stripe dashboard for shipping details.</p>
      `;
    }

    const { error } = await resend.emails.send({
      from: "PixiCards <<EMAIL>>",
      to: [TO_EMAIL],
      subject: subjectMap[body.event],
      html,
    });

    if (error) throw error;

    return new Response(JSON.stringify({ ok: true }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  } catch (err: any) {
    console.error("[notify-admin]", err);
    return new Response(JSON.stringify({ error: err?.message || String(err) }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }
});
