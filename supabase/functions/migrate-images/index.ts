import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

async function generateAndStoreImage(prompt: string, supabase: any): Promise<string> {
  console.log('Generating image with prompt:', prompt);

  // Generate image with OpenAI
  const response = await fetch('https://api.openai.com/v1/images/generations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openAIApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-image-1',
      prompt: `${prompt}, Pokemon anime style, vibrant colors, cute animated character, Japanese animation style, colorful creature design, fantasy character art`,
      size: '1024x1024',
      quality: 'high',
      n: 1,
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const imageData = data.data[0];
  let imageBlob;
  
  if (imageData.b64_json) {
    // Convert base64 to blob
    const base64Data = imageData.b64_json;
    const binaryData = atob(base64Data);
    const uint8Array = new Uint8Array(binaryData.length);
    for (let i = 0; i < binaryData.length; i++) {
      uint8Array[i] = binaryData.charCodeAt(i);
    }
    imageBlob = new Blob([uint8Array], { type: 'image/png' });
  } else if (imageData.url) {
    // Fallback: download from URL
    const imageResponse = await fetch(imageData.url);
    if (!imageResponse.ok) {
      throw new Error('Failed to download generated image');
    }
    imageBlob = await imageResponse.blob();
  } else {
    throw new Error('No image data received from OpenAI');
  }

  // Generate unique filename
  const filename = `character-${Date.now()}-${Math.random().toString(36).substring(7)}.png`;
  
  // Upload to Supabase Storage
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('character-images')
    .upload(filename, imageBlob, {
      contentType: 'image/png',
      cacheControl: '3600'
    });

  if (uploadError) {
    throw new Error(`Failed to upload image: ${uploadError.message}`);
  }

  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from('character-images')
    .getPublicUrl(filename);

  return publicUrl;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey || !supabaseUrl || !supabaseKey) {
      throw new Error('Required environment variables not configured');
    }

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);

    console.log('Starting image migration process...');

    // Find all cards with expired OpenAI URLs
    const { data: userCards, error: userCardsError } = await supabase
      .from('user_cards')
      .select('id, name, backstory, image_url')
      .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

    const { data: leaderboardCards, error: leaderboardCardsError } = await supabase
      .from('leaderboard_cards')
      .select('id, name, backstory, image_url')
      .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

    if (userCardsError || leaderboardCardsError) {
      throw new Error('Failed to fetch cards with expired URLs');
    }

    const allCards = [
      ...(userCards || []).map(card => ({ ...card, table: 'user_cards' })),
      ...(leaderboardCards || []).map(card => ({ ...card, table: 'leaderboard_cards' }))
    ];

    console.log(`Found ${allCards.length} cards with expired URLs`);

    let migratedCount = 0;
    let failedCount = 0;
    const results = [];

    // Process each card
    for (const card of allCards) {
      try {
        console.log(`Migrating card: ${card.name} (${card.table})`);
        
        // Generate prompt from existing data
        const prompt = card.backstory ? 
          `${card.name}: ${card.backstory.substring(0, 200)}` : 
          card.name;

        // Generate and store new image
        const newImageUrl = await generateAndStoreImage(prompt, supabase);

        // Update the card with new image URL
        const { error: updateError } = await supabase
          .from(card.table)
          .update({ image_url: newImageUrl })
          .eq('id', card.id);

        if (updateError) {
          throw new Error(`Failed to update card: ${updateError.message}`);
        }

        migratedCount++;
        results.push({
          id: card.id,
          name: card.name,
          table: card.table,
          status: 'success',
          newImageUrl
        });

        console.log(`Successfully migrated: ${card.name}`);

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        console.error(`Failed to migrate card ${card.name}:`, error);
        failedCount++;
        results.push({
          id: card.id,
          name: card.name,
          table: card.table,
          status: 'failed',
          error: error.message
        });
      }
    }

    console.log(`Migration completed. Migrated: ${migratedCount}, Failed: ${failedCount}`);

    return new Response(JSON.stringify({
      success: true,
      totalCards: allCards.length,
      migratedCount,
      failedCount,
      results
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in migrate-images function:', error);
    return new Response(JSON.stringify({ 
      error: error.message || 'An unexpected error occurred' 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});