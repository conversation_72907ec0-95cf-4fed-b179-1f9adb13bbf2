import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { characterName, imageDescription } = await req.json();


    const enhancedPrompt = `You are a creative writer for a kid-friendly trading card game. Generate a single-line superpower description for ${characterName}.

Character: ${characterName}
Appearance/Theme: ${imageDescription}

Requirements:
- Must be 60 characters or fewer
- Include the character's name in the description
- Format: [Character name] [action/ability] - no quotes or punctuation at the end
- Kid-friendly, positive, and exciting
- Focus on what they can DO, not accomplishments

Examples:
- <PERSON><PERSON> launches blazing fire rockets from her hands
- <PERSON><PERSON> zooms faster than lightning bolts
- <PERSON><PERSON> creates rainbow bridges in the sky
- Luna controls the power of moonbeams

Generate ONLY the superpower text for ${characterName}, no quotes, no extra text:`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'You are a creative writer for a kid-friendly trading card game. Generate single-line superpower descriptions (60 characters or fewer) that include the character\'s name and describe what they can do. No quotes, no punctuation at the end.' 
          },
          { role: 'user', content: enhancedPrompt }
        ],
        max_tokens: 50,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    let generatedStory = data.choices[0].message.content.trim();
    
    // Remove quotes and clean up the response
    generatedStory = generatedStory.replace(/["""]/g, '').replace(/\s*\(\d+\)\s*$/, '').trim();

    return new Response(JSON.stringify({ superpower: generatedStory }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in generate-story function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});