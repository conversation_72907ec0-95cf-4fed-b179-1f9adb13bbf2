import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { session_id, notify_email } = await req.json();
    if (!session_id) {
      return new Response(JSON.stringify({ error: "Missing session_id" }), {
        status: 400,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });
    }

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) {
      return new Response(JSON.stringify({ error: "Missing STRIPE_SECRET_KEY" }), {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });
    }
    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });

    // Get the checkout session and expand related objects
    const session = await stripe.checkout.sessions.retrieve(session_id, {
      expand: ["payment_intent", "customer"],
    });

    const summary = {
      id: session.id,
      status: session.status,
      payment_status: session.payment_status,
      amount_total: session.amount_total,
      currency: session.currency,
      customer_email: session.customer_details?.email || session.customer_email,
      created: session.created,
      mode: session.mode,
      total_details: session.total_details,
    } as const;

    // Fire-and-forget email to admin (works for 0-amount orders too)
    const toEmail = notify_email || "<EMAIL>"; // safe test recipient
    const html = `
      <h2>✅ PixiCards order verified</h2>
      <p><strong>Checkout Session:</strong> ${summary.id}</p>
      <p><strong>Status:</strong> ${summary.status} / ${summary.payment_status}</p>
      <p><strong>Amount:</strong> ${(summary.amount_total ?? 0) / 100} ${summary.currency?.toUpperCase() || "GBP"}</p>
      <p><strong>Customer Email:</strong> ${summary.customer_email || "(unknown)"}</p>
      <p>Note: 100% discounted orders may not create a Payment record in Stripe, but the Checkout Session is completed.</p>
    `;

    // Use waitUntil-like pattern so we return quickly
    Promise.resolve()
      .then(() => resend.emails.send({
        from: "PixiCards <<EMAIL>>",
        to: [toEmail],
        subject: "PixiCards order verified",
        html,
      }))
      .catch((e) => console.error("[verify-checkout] email error", e));

    return new Response(JSON.stringify({ ok: true, session: summary }), {
      status: 200,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  } catch (err) {
    console.error("[verify-checkout]", err);
    const message = err instanceof Error ? err.message : String(err);
    return new Response(JSON.stringify({ error: message }), {
      status: 500,
      headers: { "Content-Type": "application/json", ...corsHeaders },
    });
  }
});
