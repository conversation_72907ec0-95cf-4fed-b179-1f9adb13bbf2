import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Get all cards without image descriptions
    const { data: userCards, error: userError } = await supabase
      .from('user_cards')
      .select('id, name, image_url')
      .is('image_description', null);

    const { data: leaderboardCards, error: leaderboardError } = await supabase
      .from('leaderboard_cards')
      .select('id, name, image_url')
      .is('image_description', null);

    if (userError || leaderboardError) {
      throw new Error('Failed to fetch cards');
    }

    const allCards = [
      ...(userCards || []).map(card => ({ ...card, table: 'user_cards' })),
      ...(leaderboardCards || []).map(card => ({ ...card, table: 'leaderboard_cards' }))
    ];

    console.log(`Processing ${allCards.length} cards`);

    let processedCount = 0;
    let errorCount = 0;

    // Process cards in batches of 5 to avoid rate limits
    for (let i = 0; i < allCards.length; i += 5) {
      const batch = allCards.slice(i, i + 5);
      
      await Promise.all(batch.map(async (card) => {
        try {
          console.log(`Analyzing image for card: ${card.name}`);
          
          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${openAIApiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'gpt-4o',
              messages: [
                {
                  role: 'user',
                  content: [
                    {
                      type: 'text',
                      text: `Analyze this character image and provide a detailed description that includes:
                      - Physical appearance (clothing, colors, features, accessories)
                      - Setting/background elements
                      - Pose or action
                      - Magical or fantasy elements visible
                      - Overall mood or atmosphere
                      
                      Keep the description under 200 characters and focus on visual details that would help create a unique backstory. The character's name is "${card.name}".`
                    },
                    {
                      type: 'image_url',
                      image_url: {
                        url: card.image_url
                      }
                    }
                  ]
                }
              ],
              max_tokens: 100
            }),
          });

          if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status}`);
          }

          const data = await response.json();
          const imageDescription = data.choices[0].message.content.trim();

          // Update the card with the new description
          const { error: updateError } = await supabase
            .from(card.table)
            .update({ image_description: imageDescription })
            .eq('id', card.id);

          if (updateError) {
            throw new Error(`Failed to update card: ${updateError.message}`);
          }

          processedCount++;
          console.log(`Successfully updated ${card.name}: ${imageDescription}`);
          
        } catch (error) {
          console.error(`Error processing card ${card.name}:`, error);
          errorCount++;
        }
      }));

      // Add a small delay between batches
      if (i + 5 < allCards.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return new Response(JSON.stringify({ 
      success: true,
      processedCount,
      errorCount,
      totalCards: allCards.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
    
  } catch (error) {
    console.error('Error in analyze-card-images function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});