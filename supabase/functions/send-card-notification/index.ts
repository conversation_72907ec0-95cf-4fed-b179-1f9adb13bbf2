import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface CardData {
  id: string;
  name: string;
  signature?: string;
  image_url: string;
  backstory?: string;
  created_at: string;
  table: string;
  likes?: number;
  level?: number;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Card notification webhook received");
    
    const payload = await req.json();
    console.log("Webhook payload:", payload);

    // Extract card data from webhook payload
    const { record, table } = payload;
    
    if (!record) {
      console.error("No record found in webhook payload");
      return new Response(
        JSON.stringify({ error: "No record found in webhook payload" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

    const cardData: CardData = {
      id: record.id,
      name: record.name,
      signature: record.signature,
      image_url: record.image_url,
      backstory: record.backstory,
      created_at: record.created_at,
      table: table || 'unknown',
      likes: record.likes,
      level: record.level
    };

    // Format creation date
    const createdDate = new Date(cardData.created_at).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });

    // Create email content
    const htmlBody = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New PixiCard Created!</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="margin: 0; font-size: 28px;">🎴 New PixiCard Created!</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">A new card has been added to your collection</p>
          </div>

          <div style="background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
              ${cardData.image_url ? `<img src="${cardData.image_url}" alt="${cardData.name}" style="width: 120px; height: 120px; object-fit: cover; border-radius: 10px; border: 3px solid #667eea;">` : ''}
              <div>
                <h2 style="margin: 0 0 10px 0; color: #667eea; font-size: 24px;">${cardData.name}</h2>
                ${cardData.signature ? `<p style="margin: 5px 0; font-size: 16px;"><strong>Creator:</strong> ${cardData.signature}</p>` : ''}
                ${cardData.level ? `<p style="margin: 5px 0; font-size: 14px;"><strong>Level:</strong> ${cardData.level}</p>` : ''}
                ${cardData.likes !== undefined ? `<p style="margin: 5px 0; font-size: 14px;"><strong>Likes:</strong> ${cardData.likes}</p>` : ''}
              </div>
            </div>

            ${cardData.backstory ? `
              <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
                <h3 style="margin: 0 0 10px 0; color: #667eea; font-size: 16px;">Backstory</h3>
                <p style="margin: 0; font-style: italic; color: #666;">${cardData.backstory}</p>
              </div>
            ` : ''}
          </div>

          <div style="background: #e8f4fd; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 15px 0; color: #1976d2; font-size: 18px;">📊 Card Details</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <div><strong>Card ID:</strong><br><code style="background: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">${cardData.id}</code></div>
              <div><strong>Table:</strong><br><span style="text-transform: capitalize; color: #1976d2;">${cardData.table.replace('_', ' ')}</span></div>
              <div style="grid-column: 1 / -1;"><strong>Created:</strong><br>${createdDate}</div>
            </div>
          </div>

          <div style="text-align: center; padding: 20px 0; border-top: 1px solid #eee;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              This notification was sent automatically when a new card was created in your PixiCards database.
            </p>
          </div>
        </body>
      </html>
    `;

    const emailResponse = await resend.emails.send({
      from: "PixiCards <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `🎴 New PixiCard: "${cardData.name}" by ${cardData.signature || 'Unknown Creator'}`,
      html: htmlBody,
    });

    console.log("Email sent successfully:", emailResponse);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Card notification email sent successfully",
        emailId: emailResponse.id 
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      }
    );
  } catch (error: any) {
    console.error("Error in send-card-notification function:", error);
    return new Response(
      JSON.stringify({ 
        error: error.message,
        details: "Failed to send card notification email"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);