import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface ShippingInfo {
  fullName: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  notes: string;
}

interface CharacterData {
  name: string;
  imageUrl: string;
}

interface PrintOrderRequest {
  shippingInfo: ShippingInfo;
  characterData: CharacterData;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { shippingInfo, characterData }: PrintOrderRequest = await req.json();
    
    const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

    // Email content for the print order
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; border-bottom: 2px solid #f0f0f0; padding-bottom: 10px;">
          🎴 New Print Order Received
        </h1>
        
        <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #555; margin-top: 0;">Character Details</h2>
          <p><strong>Character Name:</strong> ${characterData.name}</p>
          <p><strong>Image URL:</strong> <a href="${characterData.imageUrl}" target="_blank">View Image</a></p>
        </div>

        <div style="background: #f0f8ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #555; margin-top: 0;">📬 Shipping Information</h2>
          <p><strong>Full Name:</strong> ${shippingInfo.fullName}</p>
          <p><strong>Email:</strong> ${shippingInfo.email}</p>
          <p><strong>Phone:</strong> ${shippingInfo.phone || "Not provided"}</p>
          
          <h3 style="color: #666; margin-top: 20px;">Address:</h3>
          <p style="margin: 5px 0;">${shippingInfo.address}</p>
          <p style="margin: 5px 0;">${shippingInfo.city}${shippingInfo.state ? `, ${shippingInfo.state}` : ""} ${shippingInfo.zipCode}</p>
          <p style="margin: 5px 0;">${shippingInfo.country}</p>
          
          ${shippingInfo.notes ? `
            <h3 style="color: #666; margin-top: 20px;">Special Instructions:</h3>
            <p style="background: white; padding: 10px; border-radius: 4px; border-left: 4px solid #4CAF50;">
              ${shippingInfo.notes}
            </p>
          ` : ""}
        </div>

        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
          <p style="margin: 0; color: #856404;">
            <strong>⏰ Order received at:</strong> ${new Date().toLocaleString()}
          </p>
        </div>
      </div>
    `;

    // Send <NAME_EMAIL>
    const emailResponse = await resend.emails.send({
      from: "Character Cards <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `🎴 New Print Order: ${characterData.name}`,
      html: emailHtml,
    });

    console.log("Print order email sent successfully:", emailResponse);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Print order submitted successfully" 
      }),
      {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      }
    );
  } catch (error: any) {
    console.error("Error in send-print-order function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);