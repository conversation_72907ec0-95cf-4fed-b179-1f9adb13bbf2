import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Starting descriptor update process...');
    
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get ALL cards from both tables to evaluate, we'll only update those over 60 chars
    const { data: userCards, error: userError } = await supabaseClient
      .from('user_cards')
      .select('id, name, backstory, image_description');

    const { data: leaderboardCards, error: leaderboardError } = await supabaseClient
      .from('leaderboard_cards')
      .select('id, name, backstory, image_description');

    if (userError || leaderboardError) {
      throw new Error(`Database error: ${userError?.message || leaderboardError?.message}`);
    }

    // Filter ONLY cards whose backstory exceeds 60 characters
    const allUserCards = (userCards || []).filter((c: any) => (c.backstory?.trim()?.length || 0) > 60);
    const allLeaderboardCards = (leaderboardCards || []).filter((c: any) => (c.backstory?.trim()?.length || 0) > 60);

    console.log(`Found ${allUserCards.length} user cards and ${allLeaderboardCards.length} leaderboard cards with backstories > 60 chars to update`);

    const generateNewStory = async (characterName: string, imageDescription: string) => {
    const prompt = `You are a creative writer for a kid-friendly trading card game. Generate a single-line superpower description for ${characterName}.

Character: ${characterName}
Appearance/Theme: ${imageDescription}

Requirements:
- Must be 60 characters or fewer
- Include the character's name in the description
- Format: [Character name] [action/ability] - no quotes or punctuation at the end
- Kid-friendly, positive, and exciting
- Focus on what they can DO, not accomplishments

Examples:
- Firey launches blazing fire rockets from her hands
- Speedy zooms faster than lightning bolts
- Magicy creates rainbow bridges in the sky
- Luna controls the power of moonbeams

Generate ONLY the superpower text for ${characterName}, no quotes, no extra text:`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openAIApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
        messages: [
          { 
            role: 'system', 
            content: 'You are a creative writer for a kid-friendly trading card game. Generate single-line superpower descriptions (60 characters or fewer) that include the character\'s name and describe what they can do. No quotes, no punctuation at the end.' 
          },
            { role: 'user', content: prompt }
          ],
          max_tokens: 50,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      let generatedStory = data.choices[0].message.content.trim();
      
      // Remove quotes and clean up the response
      generatedStory = generatedStory.replace(/["""]/g, '').replace(/\s*\(\d+\)\s*$/, '').trim();
      
      return generatedStory;
    };

    // Ensure final text is single line <= 60 chars without trailing punctuation or ellipsis
    const normalizeOneLine = (text: string): string => {
      let t = (text || '').replace(/\s+/g, ' ').trim();
      t = t.replace(/[.!?…]+$/, '');
      if (t.length > 60) {
        const cut = t.slice(0, 60);
        const lastSpace = cut.lastIndexOf(' ');
        t = (lastSpace > 0 ? cut.slice(0, lastSpace) : cut).replace(/[.!?…-]+$/, '');
      }
      return t;
    };

    let updatedUser = 0;
    let updatedLeaderboard = 0;

    // Update user cards
    for (const card of allUserCards) {
      try {
        console.log(`Updating user card: ${card.name} (current: ${card.backstory?.length} chars)`);
        const newStory = await generateNewStory(card.name, card.image_description || '');
        const finalStory = normalizeOneLine(newStory);
        
        const { error: updateError } = await supabaseClient
          .from('user_cards')
          .update({ backstory: finalStory })
          .eq('id', card.id);

        if (updateError) {
          console.error(`Error updating user card ${card.id}:`, updateError);
        } else {
          console.log(`Updated ${card.name}: "${finalStory}" (${finalStory.length} chars)`);
          updatedUser++;
        }
      } catch (error) {
        console.error(`Error processing user card ${card.id}:`, error);
      }
    }

    // Update leaderboard cards
    for (const card of allLeaderboardCards) {
      try {
        console.log(`Updating leaderboard card: ${card.name} (current: ${card.backstory?.length} chars)`);
        const newStory = await generateNewStory(card.name, card.image_description || '');
        const finalStory = normalizeOneLine(newStory);
        
        const { error: updateError } = await supabaseClient
          .from('leaderboard_cards')
          .update({ backstory: finalStory })
          .eq('id', card.id);

        if (updateError) {
          console.error(`Error updating leaderboard card ${card.id}:`, updateError);
        } else {
          console.log(`Updated ${card.name}: "${finalStory}" (${finalStory.length} chars)`);
          updatedLeaderboard++;
        }
      } catch (error) {
        console.error(`Error processing leaderboard card ${card.id}:`, error);
      }
    }

    return new Response(JSON.stringify({ 
      success: true, 
      message: `Updated ${updatedUser + updatedLeaderboard} cards successfully (User: ${updatedUser}, Leaderboard: ${updatedLeaderboard})`,
      totalFound: allUserCards.length + allLeaderboardCards.length,
      updated: updatedUser + updatedLeaderboard,
      updated_user: updatedUser,
      updated_leaderboard: updatedLeaderboard
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });


  } catch (error) {
    console.error('Error in update-long-descriptors function:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to update descriptors', 
      details: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});