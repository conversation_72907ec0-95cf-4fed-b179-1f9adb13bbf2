import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface OrderItem {
  id: string;
  name: string;
  creator: string;
  quantity: number;
  imageUrl: string;
}

interface CustomerInfo {
  name: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  specialInstructions: string;
}

interface PrintOrderRequest {
  customerInfo: CustomerInfo;
  orderItems: OrderItem[];
  totalQuantity: number;
  totalAmount: number;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { customerInfo, orderItems, totalQuantity, totalAmount }: PrintOrderRequest = await req.json();

    console.log("Processing print order:", { 
      customer: customerInfo.name, 
      totalCards: totalQuantity,
      totalAmount: totalAmount,
      itemCount: orderItems.length 
    });

    // Create order summary with pricing
    const orderItemsHtml = orderItems.map(item => {
      const itemPrice = item.quantity === 1 ? 3 : item.quantity * 2;
      const priceDetail = item.quantity === 1 ? '£3.00' : `${item.quantity}× £2.00 = £${itemPrice}.00`;
      
      return `
        <tr style="border-bottom: 1px solid #eee;">
          <td style="padding: 12px; text-align: left;">
            <img src="${item.imageUrl}" alt="${item.name}" style="width: 40px; height: 40px; border-radius: 6px; object-fit: cover; margin-right: 8px; vertical-align: middle;">
            <strong>${item.name}</strong>
          </td>
          <td style="padding: 12px; text-align: left; color: #666;">by ${item.creator}</td>
          <td style="padding: 12px; text-align: center; font-weight: bold;">${item.quantity}</td>
          <td style="padding: 12px; text-align: center; font-weight: bold; color: #e74c3c;">£${itemPrice}.00</td>
        </tr>
      `;
    }).join('');

    const emailBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; border-bottom: 2px solid #e74c3c; padding-bottom: 10px;">
          🎴 New PixiCards Print Order
        </h1>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #2c3e50; margin-top: 0;">Customer Information</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr><td style="padding: 5px 0; font-weight: bold;">Name:</td><td>${customerInfo.name}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">Email:</td><td>${customerInfo.email}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">Phone:</td><td>${customerInfo.phone || 'Not provided'}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">Address:</td><td>${customerInfo.address}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">City:</td><td>${customerInfo.city}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">State:</td><td>${customerInfo.state}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">ZIP:</td><td>${customerInfo.zip}</td></tr>
            <tr><td style="padding: 5px 0; font-weight: bold;">Country:</td><td>${customerInfo.country}</td></tr>
          </table>
        </div>

        <div style="background: #fff; border: 1px solid #ddd; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #2c3e50; margin: 0; padding: 15px; background: #f1f2f6; border-bottom: 1px solid #ddd; border-radius: 8px 8px 0 0;">
            Order Details
          </h2>
          
          <div style="padding: 15px;">
            <div style="display: flex; justify-content: space-between; margin: 0 0 15px 0;">
              <p style="margin: 0; font-size: 16px;">
                <strong>Total Cards:</strong> ${totalQuantity} cards
              </p>
              <p style="margin: 0; font-size: 18px; color: #e74c3c;">
                <strong>Total Cost: £${totalAmount.toFixed(2)}</strong>
              </p>
            </div>
            <p style="margin: 0 0 15px 0; font-size: 12px; color: #666;">
              Pricing: £3 per card • £2 each when ordering 2+ of same card
            </p>
            
            <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
              <thead>
                <tr style="background: #f8f9fa;">
                  <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Character</th>
                  <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Creator</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Quantity</th>
                  <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Price</th>
                </tr>
              </thead>
              <tbody>
                ${orderItemsHtml}
              </tbody>
            </table>
          </div>
        </div>

        ${customerInfo.specialInstructions ? `
          <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <h3 style="color: #856404; margin-top: 0;">Special Instructions</h3>
            <p style="color: #856404; margin: 0;">${customerInfo.specialInstructions}</p>
          </div>
        ` : ''}

        <div style="background: #e8f5e8; border: 1px solid #c3e6c3; border-radius: 8px; padding: 15px; margin: 20px 0;">
          <p style="color: #2d5a2d; margin: 0;">
            <strong>🚀 Next Steps:</strong> Review this order and reach out to the customer to confirm details and arrange payment/shipping.
          </p>
        </div>

        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <p style="color: #666; font-size: 12px; text-align: center;">
          This order was submitted through the PixiCards print order system.
        </p>
      </div>
    `;

    const emailResponse = await resend.emails.send({
      from: "PixiCards Orders <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: `🎴 New Print Order - ${totalQuantity} cards (£${totalAmount.toFixed(2)}) from ${customerInfo.name}`,
      html: emailBody,
    });

    console.log("Print order email sent successfully:", emailResponse);

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Print order submitted successfully",
        emailId: emailResponse.data?.id 
      }), 
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (error: any) {
    console.error("Error in send-bulk-order function:", error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false 
      }), 
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
};

serve(handler);