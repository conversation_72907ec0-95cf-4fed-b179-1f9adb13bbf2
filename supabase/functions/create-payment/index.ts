import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface LineItem {
  name: string;
  amount: number; // in major currency units (e.g., 15.00 GBP => 15)
  quantity: number;
  image?: string;
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") ?? "";
    const supabaseAnon = Deno.env.get("SUPABASE_ANON_KEY") ?? "";
    const stripeSecret = Deno.env.get("STRIPE_SECRET_KEY") ?? "";

    if (!stripeSecret) {
      return new Response(JSON.stringify({ error: "Missing STRIPE_SECRET_KEY" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      });
    }

    const supabase = createClient(supabaseUrl, supabaseAnon);

    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      return new Response(JSON.stringify({ error: "Not authenticated" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 401,
      });
    }

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabase.auth.getUser(token);
    if (userError || !userData.user) {
      return new Response(JSON.stringify({ error: "Authentication failed" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 401,
      });
    }

    const body = await req.json().catch(() => ({}));
    const items: LineItem[] = Array.isArray(body.items) ? body.items : [];
    const currency = (body.currency || "gbp").toLowerCase();
    const customerEmail = body.customerEmail || userData.user.email || undefined;
    const allowCountries: string[] = body.allowedCountries || [
      "GB","US","CA","AU","IE","NZ","FR","DE","ES","IT","NL","SE","NO","DK","FI","BE","AT","CH","PT"
    ];

    if (items.length === 0) {
      return new Response(JSON.stringify({ error: "No items provided" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      });
    }

    const stripe = new Stripe(stripeSecret, { apiVersion: "2023-10-16" });

    // Build Stripe line items (amount in smallest currency unit)
    const stripeLineItems = items.map((it) => ({
      price_data: {
        currency,
        product_data: {
          name: it.name,
          images: it.image ? [it.image] : undefined,
        },
        unit_amount: Math.round(it.amount * 100),
      },
      quantity: Math.max(1, Number(it.quantity || 1)),
    }));

    const origin = Deno.env.get("PUBLIC_SITE_URL") || req.headers.get("origin") || "https://pixicards.co";

    const session = await stripe.checkout.sessions.create({
      customer_email: customerEmail,
      mode: "payment",
      line_items: stripeLineItems,
      allow_promotion_codes: true,
      success_url: `${origin}/order?status=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/order?status=cancelled`,
      shipping_address_collection: { allowed_countries: allowCountries as any },
      phone_number_collection: { enabled: true },
      metadata: {
        source: "pixicards",
        order_context: body.orderContext ? JSON.stringify(body.orderContext) : undefined,
      },
    });

    return new Response(JSON.stringify({ url: session.url }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (err) {
    console.error("[create-payment] Error:", err);
    const message = err instanceof Error ? err.message : String(err);
    return new Response(JSON.stringify({ error: message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});