-- PHASE 1: CRITIC<PERSON> DATA PROTECTION FIXES

-- Fix 1: Remove public access to profiles table - only allow users to see their own profile
DROP POLICY IF EXISTS "Anyone can view profiles" ON public.profiles;

-- Users can only view their own profile 
CREATE POLICY "Users can view their own profile only" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = id);

-- Fix 2: Remove public admin access to prompt_history - restrict to system only
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all prompt history" ON public.prompt_history;

-- Only allow system operations and users to view their own prompt history
CREATE POLICY "Users can view their own prompt history" 
ON public.prompt_history 
FOR SELECT 
USING (auth.uid() = user_id OR user_id IS NULL);

-- <PERSON><PERSON> can view prompt history only through proper role verification
CREATE POLICY "Verified admins can view prompt history" 
ON public.prompt_history 
FOR SELECT 
USING (has_role(auth.uid(), 'admin'::app_role));

-- Fix 3: Secure bulk orders - remove unrestricted access
-- Keep existing policies but ensure they're properly restrictive

-- Fix 4: Add proper search_path to existing functions to prevent SQL injection
CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _role app_role)
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role = _role
  )
$$;

CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT public.has_role(auth.uid(), 'admin'::app_role)
$$;

CREATE OR REPLACE FUNCTION public.grant_admin_role(_user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Only existing admins can grant admin roles
  IF NOT public.has_role(auth.uid(), 'admin'::app_role) THEN
    RAISE EXCEPTION 'Only admins can grant admin roles';
  END IF;
  
  INSERT INTO public.user_roles (user_id, role, granted_by)
  VALUES (_user_id, 'admin'::app_role, auth.uid())
  ON CONFLICT (user_id, role) DO NOTHING;
  
  RETURN true;
END;
$$;

CREATE OR REPLACE FUNCTION public.revoke_admin_role(_user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Only existing admins can revoke admin roles
  IF NOT public.has_role(auth.uid(), 'admin'::app_role) THEN
    RAISE EXCEPTION 'Only admins can revoke admin roles';
  END IF;
  
  -- Prevent self-removal of admin role
  IF _user_id = auth.uid() THEN
    RAISE EXCEPTION 'Cannot revoke your own admin role';
  END IF;
  
  DELETE FROM public.user_roles 
  WHERE user_id = _user_id AND role = 'admin'::app_role;
  
  RETURN true;
END;
$$;