
-- Create profiles table for user data
CREATE TABLE public.profiles (
  id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view their own profile" 
  ON public.profiles FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
  ON public.profiles FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Anyone can view profiles" 
  ON public.profiles FOR SELECT 
  USING (true);

-- Add user_id to user_cards and make it NOT NULL
ALTER TABLE public.user_cards 
  ADD COLUMN user_id UUID REFERENCES auth.users ON DELETE CASCADE;

-- Update existing user_cards to use a default user (you'll need to handle existing data)
-- For now, we'll keep session_id for backward compatibility

-- Add user_id to leaderboard_cards
ALTER TABLE public.leaderboard_cards 
  ADD COLUMN user_id UUID REFERENCES auth.users ON DELETE SET NULL;

-- Update RLS policies for user_cards to use user_id when available
DROP POLICY IF EXISTS "Anyone can view user cards" ON public.user_cards;
DROP POLICY IF EXISTS "Anyone can insert user cards" ON public.user_cards;
DROP POLICY IF EXISTS "Anyone can update user cards" ON public.user_cards;
DROP POLICY IF EXISTS "Anyone can delete user cards" ON public.user_cards;

-- New policies for user_cards
CREATE POLICY "Users can view their own cards" 
  ON public.user_cards FOR SELECT 
  USING (
    auth.uid() = user_id OR 
    (user_id IS NULL AND session_id IS NOT NULL)
  );

CREATE POLICY "Users can insert their own cards" 
  ON public.user_cards FOR INSERT 
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update their own cards" 
  ON public.user_cards FOR UPDATE 
  USING (
    auth.uid() = user_id OR 
    (user_id IS NULL AND session_id IS NOT NULL)
  );

CREATE POLICY "Users can delete their own cards" 
  ON public.user_cards FOR DELETE 
  USING (
    auth.uid() = user_id OR 
    (user_id IS NULL AND session_id IS NOT NULL)
  );

-- Create function to auto-create profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, username)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'username'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Add likes tracking table for users
CREATE TABLE public.user_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users ON DELETE CASCADE,
  card_id UUID NOT NULL REFERENCES public.leaderboard_cards ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, card_id)
);

-- Enable RLS on user_likes
ALTER TABLE public.user_likes ENABLE ROW LEVEL SECURITY;

-- Policies for user_likes
CREATE POLICY "Users can view their own likes" 
  ON public.user_likes FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own likes" 
  ON public.user_likes FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes" 
  ON public.user_likes FOR DELETE 
  USING (auth.uid() = user_id);
