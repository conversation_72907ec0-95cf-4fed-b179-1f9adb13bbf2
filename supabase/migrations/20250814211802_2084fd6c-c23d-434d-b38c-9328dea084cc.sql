-- Fix remaining search_path security issues for all functions

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_current_month_start()
RETURNS date
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  RETURN date_trunc('month', now())::date;
END;
$$;

CREATE OR REPLACE FUNCTION public.ensure_minimum_likes()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- If the card is more than a day old and has less than 5 likes, set minimum to 5
  IF NEW.created_at < (now() - interval '1 day') THEN
    NEW.likes = GREATEST(NEW.likes, 5);
    NEW.weekly_likes = GREATEST(NEW.weekly_likes, 5);
  END IF;
  
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.reset_monthly_likes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  UPDATE public.leaderboard_cards
  SET monthly_likes = 0,
      month_start_date = date_trunc('month', now())::date;
END;
$$;

CREATE OR REPLACE FUNCTION public.log_order_submission()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Log order submission for security monitoring
  INSERT INTO public.prompt_history (
    user_prompt, 
    processed_prompt, 
    generation_status,
    metadata,
    session_id
  ) VALUES (
    'Order submission audit',
    CONCAT('Order: ', NEW.id),
    'success',
    jsonb_build_object(
      'event_type', 'order_submission',
      'order_id', NEW.id,
      'customer_email_hash', encode(digest(NEW.customer_email, 'sha256'), 'hex'),
      'total_quantity', NEW.total_quantity,
      'total_amount', NEW.total_amount,
      'submission_time', NEW.created_at,
      'customer_country', NEW.customer_country
    ),
    'security_audit'
  );
  
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.reset_weekly_likes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Reset all weekly likes to 0 and update week start date
  UPDATE public.leaderboard_cards 
  SET 
    weekly_likes = 0,
    week_start_date = date_trunc('week', now())::date;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_current_week_start()
RETURNS date
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  RETURN date_trunc('week', now())::date;
END;
$$;

CREATE OR REPLACE FUNCTION public.log_admin_action()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- Log admin actions for security monitoring
  INSERT INTO public.prompt_history (
    user_prompt, 
    processed_prompt, 
    generation_status,
    metadata,
    session_id,
    user_id
  ) VALUES (
    'Admin action logged',
    CONCAT('Role change: ', TG_OP),
    'success',
    jsonb_build_object(
      'event_type', 'admin_action',
      'table_name', TG_TABLE_NAME,
      'operation', TG_OP,
      'target_user_id', COALESCE(NEW.user_id, OLD.user_id),
      'role', COALESCE(NEW.role, OLD.role),
      'admin_user_id', auth.uid(),
      'timestamp', NOW()
    ),
    'admin_audit',
    auth.uid()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

CREATE OR REPLACE FUNCTION public.truncate_backstory()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- If backstory is longer than 150 characters, truncate it
  IF NEW.backstory IS NOT NULL AND LENGTH(NEW.backstory) > 150 THEN
    NEW.backstory = LEFT(NEW.backstory, 147) || '...';
  END IF;
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.notify_card_creation()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
  card_data jsonb;
  webhook_url text;
  response_id bigint;
BEGIN
  -- Build the card data JSON
  card_data := jsonb_build_object(
    'id', NEW.id,
    'name', NEW.name,
    'signature', NEW.signature,
    'image_url', NEW.image_url,
    'backstory', NEW.backstory,
    'created_at', NEW.created_at,
    'likes', NEW.likes,
    'level', NEW.level
  );

  -- Set the webhook URL
  webhook_url := 'https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification';

  -- Use pg_net to make HTTP request to edge function with error handling
  BEGIN
    SELECT net.http_post(
      url := webhook_url,
      headers := '{"Content-Type": "application/json"}'::jsonb,
      body := jsonb_build_object(
        'record', card_data,
        'table', TG_TABLE_NAME
      )
    ) INTO response_id;
    
    -- Log successful notification
    RAISE LOG 'Card notification sent successfully, response_id: %', response_id;
    
  EXCEPTION WHEN OTHERS THEN
    -- Log the error but don't fail the card creation
    RAISE LOG 'Failed to send card notification: %', SQLERRM;
    -- Continue with the card creation even if notification fails
  END;

  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, username)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'username'
  );
  RETURN NEW;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_next_character_level(character_name text)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    next_level INTEGER;
BEGIN
    -- Count existing cards with the same name across both tables
    SELECT COALESCE(MAX(combined_level), 0) + 1 INTO next_level
    FROM (
        SELECT level as combined_level FROM public.user_cards WHERE LOWER(name) = LOWER(character_name)
        UNION ALL
        SELECT level as combined_level FROM public.leaderboard_cards WHERE LOWER(name) = LOWER(character_name)
    ) as combined_cards;
    
    RETURN next_level;
END;
$$;