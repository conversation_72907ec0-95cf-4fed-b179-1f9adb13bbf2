-- Fix the security warning by setting search_path for the notification function
CREATE OR REPLACE FUNCTION notify_card_creation()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = 'public'
AS $$
DECLARE
  card_data jsonb;
  webhook_url text;
BEGIN
  -- Build the card data JSON
  card_data := jsonb_build_object(
    'id', NEW.id,
    'name', NEW.name,
    'signature', NEW.signature,
    'image_url', NEW.image_url,
    'backstory', NEW.backstory,
    'created_at', NEW.created_at,
    'likes', NEW.likes,
    'level', NEW.level
  );

  -- Set the webhook URL
  webhook_url := 'https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification';

  -- Use pg_net to make HTTP request to edge function
  PERFORM
    net.http_post(
      url := webhook_url,
      headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.settings.service_role_key', true) || '"}'::jsonb,
      body := jsonb_build_object(
        'record', card_data,
        'table', TG_TABLE_NAME
      )
    );

  RETURN NEW;
END;
$$;