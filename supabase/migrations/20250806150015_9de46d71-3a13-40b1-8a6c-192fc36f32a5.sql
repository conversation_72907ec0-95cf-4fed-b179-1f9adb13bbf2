-- Fix the security warning for the ensure_minimum_likes function
-- First drop the trigger that depends on the function
DROP TRIGGER IF EXISTS ensure_minimum_likes_trigger ON public.user_cards;
DROP TRIGGER IF EXISTS ensure_minimum_likes_trigger ON public.leaderboard_cards;

-- Now drop the function
DROP FUNCTION IF EXISTS public.ensure_minimum_likes();

-- Recreate the function with proper security settings
CREATE OR REPLACE FUNCTION public.ensure_minimum_likes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
BEGIN
  -- If the card is more than a day old and has less than 5 likes, set minimum to 5
  IF NEW.created_at < (now() - interval '1 day') THEN
    NEW.likes = GREATEST(NEW.likes, 5);
    NEW.weekly_likes = GREATEST(NEW.weekly_likes, 5);
  END IF;
  
  RETURN NEW;
END;
$function$;

-- Recreate the triggers
CREATE TRIGGER ensure_minimum_likes_trigger
  BEFORE INSERT OR UPDATE ON public.user_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.ensure_minimum_likes();

CREATE TRIGGER ensure_minimum_likes_trigger
  BEFORE INSERT OR UPDATE ON public.leaderboard_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.ensure_minimum_likes();