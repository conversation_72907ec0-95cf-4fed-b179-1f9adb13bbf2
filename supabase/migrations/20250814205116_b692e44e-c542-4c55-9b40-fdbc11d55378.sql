-- Fix critical security vulnerability in bulk_orders table
-- Remove public read access and implement proper access controls

-- First, drop the existing public read policy
DROP POLICY IF EXISTS "Anyone can view bulk orders" ON public.bulk_orders;

-- Create a new policy that only allows authenticated admin users to view orders
-- For now, we'll restrict to service role access only until a proper admin role system is implemented
CREATE POLICY "Service role can view bulk orders" 
ON public.bulk_orders 
FOR SELECT 
USING (false); -- This effectively blocks all reads unless bypassed by service role

-- Keep the existing insert policy since the ordering functionality needs it
-- The "Anyone can insert bulk orders" policy remains unchanged to preserve functionality

-- Add a comment explaining the security restriction
COMMENT ON TABLE public.bulk_orders IS 'Contains sensitive customer PII. Access restricted to service role and admin functions only.';