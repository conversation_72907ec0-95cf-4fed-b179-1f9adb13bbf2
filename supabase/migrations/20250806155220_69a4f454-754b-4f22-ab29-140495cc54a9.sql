-- Create prompt_history table for comprehensive tracking
CREATE TABLE public.prompt_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_prompt TEXT NOT NULL,
  processed_prompt TEXT NOT NULL,
  dalle_revised_prompt TEXT,
  image_url TEXT,
  card_id UUID,
  user_id UUID,
  session_id TEXT,
  generation_status TEXT NOT NULL DEFAULT 'pending',
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.prompt_history ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access
CREATE POLICY "Ad<PERSON> can view all prompt history" 
ON public.prompt_history 
FOR SELECT 
USING (true);

CREATE POLICY "System can insert prompt history" 
ON public.prompt_history 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "System can update prompt history" 
ON public.prompt_history 
FOR UPDATE 
USING (true);

-- Create indexes for better performance
CREATE INDEX idx_prompt_history_user_id ON public.prompt_history(user_id);
CREATE INDEX idx_prompt_history_session_id ON public.prompt_history(session_id);
CREATE INDEX idx_prompt_history_status ON public.prompt_history(generation_status);
CREATE INDEX idx_prompt_history_created_at ON public.prompt_history(created_at DESC);
CREATE INDEX idx_prompt_history_card_id ON public.prompt_history(card_id);

-- Add trigger for automatic timestamp updates
CREATE TRIGGER update_prompt_history_updated_at
BEFORE UPDATE ON public.prompt_history
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();