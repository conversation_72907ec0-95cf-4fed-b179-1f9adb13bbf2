-- Enable pg_cron extension for scheduled tasks
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Add weekly tracking columns to leaderboard_cards
ALTER TABLE public.leaderboard_cards 
ADD COLUMN weekly_likes integer NOT NULL DEFAULT 0,
ADD COLUMN week_start_date date NOT NULL DEFAULT date_trunc('week', now())::date;

-- Create function to reset weekly likes every Sunday
CREATE OR REPLACE FUNCTION public.reset_weekly_likes()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Reset all weekly likes to 0 and update week start date
  UPDATE public.leaderboard_cards 
  SET 
    weekly_likes = 0,
    week_start_date = date_trunc('week', now())::date;
END;
$$;

-- Create function to get current week boundaries
CREATE OR REPLACE FUNCTION public.get_current_week_start()
RETURNS date
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN date_trunc('week', now())::date;
END;
$$;

-- Schedule weekly reset every Sunday at 11:59 PM
SELECT cron.schedule(
  'reset-weekly-likes',
  '59 23 * * 0',  -- Every Sunday at 11:59 PM
  'SELECT public.reset_weekly_likes();'
);