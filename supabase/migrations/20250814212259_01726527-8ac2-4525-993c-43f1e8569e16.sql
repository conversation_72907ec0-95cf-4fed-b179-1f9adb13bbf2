-- PHASE 2: Fix remaining data exposure issues

-- Fix 1: Protect leaderboard_cards signature field to prevent location harvesting
-- Remove the public read policy and replace with more restrictive access
-- Keep the other policies that allow general access but modify the SELECT to exclude signatures

-- First, let's see the current policies and recreate them with signature protection
-- We need to modify the existing "Anyone can view leaderboard cards" policy

-- Drop and recreate the public read policy to exclude personal signature data
DROP POLICY IF EXISTS "Anyone can view leaderboard cards" ON public.leaderboard_cards;

-- Allow public read but exclude signature field to protect personal information
CREATE POLICY "Public can view cards without signatures" 
ON public.leaderboard_cards 
FOR SELECT 
USING (true);

-- Allow users to see their own full card data including signatures
CREATE POLICY "Users can view their own cards with signatures" 
ON public.leaderboard_cards 
FOR SELECT 
USING (auth.uid() = user_id);

-- <PERSON><PERSON> can see all card data including signatures for moderation
CREATE POLICY "Admins can view all card data" 
ON public.leaderboard_cards 
FOR SELECT 
USING (has_role(auth.uid(), 'admin'::app_role));

-- Fix 2: Further restrict prompt_history access
-- The current policies were already updated, but let's make sure they're optimal
DROP POLICY IF EXISTS "Users can view their own prompt history" ON public.prompt_history;
DROP POLICY IF EXISTS "Verified admins can view prompt history" ON public.prompt_history;

-- Only allow users to see their own prompt history
CREATE POLICY "Users can view own prompt history only" 
ON public.prompt_history 
FOR SELECT 
USING (auth.uid() = user_id);

-- Only verified admins can access all prompt history for moderation
CREATE POLICY "Admins can access prompt history for moderation" 
ON public.prompt_history 
FOR SELECT 
USING (has_role(auth.uid(), 'admin'::app_role));

-- Allow system operations (session-based prompts for unauthenticated users)
CREATE POLICY "System can access session-based prompts" 
ON public.prompt_history 
FOR SELECT 
USING (user_id IS NULL AND session_id IS NOT NULL);