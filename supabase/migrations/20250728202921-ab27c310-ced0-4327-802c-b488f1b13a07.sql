-- Fix security warning: Set search_path for the truncate_backstory function
CREATE OR REPLACE FUNCTION public.truncate_backstory()
RETURNS TRIGGER 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  -- If backstory is longer than 150 characters, truncate it
  IF NEW.backstory IS NOT NULL AND LENGTH(NEW.backstory) > 150 THEN
    NEW.backstory = LEFT(NEW.backstory, 147) || '...';
  END IF;
  RETURN NEW;
END;
$$;