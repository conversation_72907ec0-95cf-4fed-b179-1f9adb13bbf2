-- Update existing cards that have less than 5 likes and are more than a day old
UPDATE public.leaderboard_cards 
SET likes = GREATEST(likes, 5),
    weekly_likes = GREATEST(weekly_likes, 5)
WHERE created_at < (now() - interval '1 day') 
  AND (likes < 5 OR weekly_likes < 5);

-- Create a function to ensure minimum likes for day-old cards
CREATE OR REPLACE FUNCTION public.ensure_minimum_likes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
BEGIN
  -- If the card is more than a day old and has less than 5 likes, set minimum to 5
  IF NEW.created_at < (now() - interval '1 day') THEN
    NEW.likes = GREATEST(NEW.likes, 5);
    NEW.weekly_likes = GREATEST(NEW.weekly_likes, 5);
  END IF;
  
  RETURN NEW;
END;
$function$;

-- Create trigger to automatically ensure minimum likes on updates
CREATE TRIGGER ensure_minimum_likes_trigger
  BEFORE UPDATE ON public.leaderboard_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.ensure_minimum_likes();