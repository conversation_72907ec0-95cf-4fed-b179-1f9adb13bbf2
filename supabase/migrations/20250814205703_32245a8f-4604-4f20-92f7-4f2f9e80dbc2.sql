-- Create proper admin role system and secure RLS policies for bulk_orders
-- This replaces the overly restrictive "false" policy with proper role-based access

-- Step 1: Create user roles system
CREATE TYPE public.app_role AS ENUM ('admin', 'moderator', 'user');

-- Create user_roles table to manage access
CREATE TABLE public.user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role app_role NOT NULL,
    granted_by UUID REFERENCES auth.users(id),
    granted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    UNIQUE (user_id, role)
);

-- Enable RLS on user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create security definer function to check user roles (prevents RLS recursion)
CREATE OR REPLACE FUNCTION public.has_role(_user_id UUID, _role app_role)
R<PERSON>URNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role = _role
  )
$$;

-- Create convenience function to check if current user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT public.has_role(auth.uid(), 'admin'::app_role)
$$;

-- RLS policies for user_roles table
CREATE POLICY "Users can view their own roles"
ON public.user_roles
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all roles"
ON public.user_roles
FOR SELECT
USING (public.has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can insert roles"
ON public.user_roles
FOR INSERT
WITH CHECK (public.has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can update roles"
ON public.user_roles
FOR UPDATE
USING (public.has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Admins can delete roles"
ON public.user_roles
FOR DELETE
USING (public.has_role(auth.uid(), 'admin'::app_role));

-- Step 2: Update bulk_orders RLS policies with proper role-based access
DROP POLICY IF EXISTS "Service role can view bulk orders" ON public.bulk_orders;

-- Create new secure policies for bulk_orders
CREATE POLICY "Admins can view all bulk orders"
ON public.bulk_orders
FOR SELECT
USING (public.has_role(auth.uid(), 'admin'::app_role));

CREATE POLICY "Moderators can view bulk orders"
ON public.bulk_orders
FOR SELECT
USING (public.has_role(auth.uid(), 'moderator'::app_role));

-- Allow admins to update order status and details
CREATE POLICY "Admins can update bulk orders"
ON public.bulk_orders
FOR UPDATE
USING (public.has_role(auth.uid(), 'admin'::app_role));

-- Allow admins to delete orders if needed
CREATE POLICY "Admins can delete bulk orders"
ON public.bulk_orders
FOR DELETE
USING (public.has_role(auth.uid(), 'admin'::app_role));

-- Step 3: Create admin management functions
CREATE OR REPLACE FUNCTION public.grant_admin_role(_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Only existing admins can grant admin roles
  IF NOT public.has_role(auth.uid(), 'admin'::app_role) THEN
    RAISE EXCEPTION 'Only admins can grant admin roles';
  END IF;
  
  INSERT INTO public.user_roles (user_id, role, granted_by)
  VALUES (_user_id, 'admin'::app_role, auth.uid())
  ON CONFLICT (user_id, role) DO NOTHING;
  
  RETURN true;
END;
$$;

CREATE OR REPLACE FUNCTION public.revoke_admin_role(_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Only existing admins can revoke admin roles
  IF NOT public.has_role(auth.uid(), 'admin'::app_role) THEN
    RAISE EXCEPTION 'Only admins can revoke admin roles';
  END IF;
  
  -- Prevent self-removal of admin role
  IF _user_id = auth.uid() THEN
    RAISE EXCEPTION 'Cannot revoke your own admin role';
  END IF;
  
  DELETE FROM public.user_roles 
  WHERE user_id = _user_id AND role = 'admin'::app_role;
  
  RETURN true;
END;
$$;

-- Step 4: Add security audit logging for admin actions
CREATE OR REPLACE FUNCTION public.log_admin_action()
RETURNS TRIGGER AS $$
BEGIN
  -- Log admin actions for security monitoring
  INSERT INTO public.prompt_history (
    user_prompt, 
    processed_prompt, 
    generation_status,
    metadata,
    session_id,
    user_id
  ) VALUES (
    'Admin action logged',
    CONCAT('Role change: ', TG_OP),
    'success',
    jsonb_build_object(
      'event_type', 'admin_action',
      'table_name', TG_TABLE_NAME,
      'operation', TG_OP,
      'target_user_id', COALESCE(NEW.user_id, OLD.user_id),
      'role', COALESCE(NEW.role, OLD.role),
      'admin_user_id', auth.uid(),
      'timestamp', NOW()
    ),
    'admin_audit',
    auth.uid()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Add audit trigger for role changes
CREATE TRIGGER audit_admin_actions
  AFTER INSERT OR UPDATE OR DELETE ON public.user_roles
  FOR EACH ROW
  EXECUTE FUNCTION public.log_admin_action();

-- Add helpful comments
COMMENT ON TABLE public.user_roles IS 'User role management system for secure access control to sensitive data like customer orders.';
COMMENT ON FUNCTION public.has_role IS 'Security definer function to check user roles - prevents RLS recursion issues.';
COMMENT ON TYPE public.app_role IS 'Application roles: admin (full access), moderator (read orders), user (default).';

-- Update bulk_orders table comment
COMMENT ON TABLE public.bulk_orders IS 'Customer orders with PII protection: Only admins and moderators can view, admins can modify. All submissions audited.';

-- Show current security status
SELECT 'Security system installed. No admin users exist yet. Use grant_admin_role() function to create first admin.' as status;