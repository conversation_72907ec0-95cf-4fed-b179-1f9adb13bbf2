-- Create bulk_orders table for storing order information
CREATE TABLE public.bulk_orders (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_name TEXT NOT NULL,
  customer_email TEXT NOT NULL,
  customer_address TEXT NOT NULL,
  customer_city TEXT NOT NULL,
  customer_state TEXT NOT NULL,
  customer_zip TEXT NOT NULL,
  customer_country TEXT NOT NULL DEFAULT 'United States',
  customer_phone TEXT,
  special_instructions TEXT,
  order_items JSONB NOT NULL, -- Array of {card_id, card_name, creator, quantity, image_url}
  total_quantity INTEGER NOT NULL,
  total_amount DECIMAL(10,2) DEFAULT 0, -- For future pricing
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.bulk_orders ENABLE ROW LEVEL SECURITY;

-- Create policy to allow anyone to insert orders (public functionality)
CREATE POLICY "Anyone can insert bulk orders" 
ON public.bulk_orders 
FOR INSERT 
WITH CHECK (true);

-- Create policy to allow viewing orders (for admin purposes)
CREATE POLICY "Anyone can view bulk orders" 
ON public.bulk_orders 
FOR SELECT 
USING (true);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_bulk_orders_updated_at
  BEFORE UPDATE ON public.bulk_orders
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();