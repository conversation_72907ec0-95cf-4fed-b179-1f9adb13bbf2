-- Create weekly_winners table to track historical weekly winners
CREATE TABLE public.weekly_winners (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  card_id UUID NOT NULL,
  week_start_date DATE NOT NULL,
  week_end_date DATE NOT NULL,
  final_likes INTEGER NOT NULL,
  card_name TEXT NOT NULL,
  card_creator TEXT,
  card_location TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.weekly_winners ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing weekly winners
CREATE POLICY "Anyone can view weekly winners" 
ON public.weekly_winners 
FOR SELECT 
USING (true);

-- Update Leah's likes to 52 (she's the last week's winner)
UPDATE public.leaderboard_cards 
SET likes = 52, weekly_likes = 0, updated_at = now()
WHERE name = '<PERSON>';

-- Boost other cards' likes by 10x
UPDATE public.leaderboard_cards 
SET 
  likes = CASE 
    WHEN likes = 0 THEN FLOOR(RANDOM() * 5) + 1  -- Give 0-like cards 1-5 likes
    ELSE likes * 10 
  END,
  weekly_likes = CASE 
    WHEN weekly_likes = 0 THEN FLOOR(RANDOM() * 10) + 1  -- Give some weekly variety
    ELSE weekly_likes * 10 
  END,
  updated_at = now()
WHERE name != 'Leah';

-- Insert Leah as last week's winner
INSERT INTO public.weekly_winners (
  card_id, 
  week_start_date, 
  week_end_date, 
  final_likes, 
  card_name, 
  card_creator, 
  card_location
) 
SELECT 
  id,
  (CURRENT_DATE - INTERVAL '7 days')::date,
  (CURRENT_DATE - INTERVAL '1 day')::date,
  52,
  'Leah',
  'Louis',
  'Ohio'
FROM public.leaderboard_cards 
WHERE name = 'Leah';