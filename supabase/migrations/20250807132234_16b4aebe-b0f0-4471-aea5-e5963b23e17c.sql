-- Create database webhooks for card creation notifications

-- Insert webhook configurations for both card tables
INSERT INTO supabase_functions.hooks (hook_table_id, hook_name, events, config)
VALUES 
  (
    (SELECT id FROM information_schema.tables WHERE table_name = 'leaderboard_cards' AND table_schema = 'public'),
    'send_card_notification_leaderboard',
    ARRAY['INSERT'],
    '{"url": "https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification", "method": "POST"}'::jsonb
  ),
  (
    (SELECT id FROM information_schema.tables WHERE table_name = 'user_cards' AND table_schema = 'public'),
    'send_card_notification_user',
    ARRAY['INSERT'], 
    '{"url": "https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification", "method": "POST"}'::jsonb
  )
ON CONFLICT DO NOTHING;