-- Create notification function that calls the edge function
CREATE OR REPLACE FUNCTION notify_card_creation()
RETURNS TRIGGER AS $$
DECLARE
  card_data jsonb;
  webhook_url text;
BEGIN
  -- Build the card data JSON
  card_data := jsonb_build_object(
    'id', NEW.id,
    'name', NEW.name,
    'signature', NEW.signature,
    'image_url', NEW.image_url,
    'backstory', NEW.backstory,
    'created_at', NEW.created_at,
    'likes', NEW.likes,
    'level', NEW.level
  );

  -- Set the webhook URL
  webhook_url := 'https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification';

  -- Use pg_net to make HTTP request to edge function
  PERFORM
    net.http_post(
      url := webhook_url,
      headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('app.settings.service_role_key', true) || '"}'::jsonb,
      body := jsonb_build_object(
        'record', card_data,
        'table', TG_TABLE_NAME
      )
    );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for both tables
CREATE TRIGGER trigger_notify_leaderboard_card_creation
  AFTER INSERT ON leaderboard_cards
  FOR EACH ROW
  EXECUTE FUNCTION notify_card_creation();

CREATE TRIGGER trigger_notify_user_card_creation
  AFTER INSERT ON user_cards
  FOR EACH ROW
  EXECUTE FUNCTION notify_card_creation();