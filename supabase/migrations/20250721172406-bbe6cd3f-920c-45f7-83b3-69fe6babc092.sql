
-- Add level column to user_cards table
ALTER TABLE public.user_cards 
ADD COLUMN level integer NOT NULL DEFAULT 1;

-- Add level column to leaderboard_cards table
ALTER TABLE public.leaderboard_cards 
ADD COLUMN level integer NOT NULL DEFAULT 1;

-- Create a function to calculate the next level for a character name
CREATE OR <PERSON><PERSON>LACE FUNCTION get_next_character_level(character_name TEXT)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    next_level INTEGER;
BEGIN
    -- Count existing cards with the same name across both tables
    SELECT COALESCE(MAX(combined_level), 0) + 1 INTO next_level
    FROM (
        SELECT level as combined_level FROM user_cards WHERE LOWER(name) = LOWER(character_name)
        UNION ALL
        SELECT level as combined_level FROM leaderboard_cards WHERE LOWER(name) = LOWER(character_name)
    ) as combined_cards;
    
    RETURN next_level;
END;
$$;
