-- Enable the pg_net extension to allow HTTP requests from database functions
CREATE EXTENSION IF NOT EXISTS pg_net;

-- Update the notification function with better error handling
CREATE OR REPLACE FUNCTION notify_card_creation()
RETURNS TRIGGER 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = 'public'
AS $$
DECLARE
  card_data jsonb;
  webhook_url text;
  response_id bigint;
BEGIN
  -- Build the card data JSON
  card_data := jsonb_build_object(
    'id', NEW.id,
    'name', NEW.name,
    'signature', NEW.signature,
    'image_url', NEW.image_url,
    'backstory', NEW.backstory,
    'created_at', NEW.created_at,
    'likes', NEW.likes,
    'level', NEW.level
  );

  -- Set the webhook URL
  webhook_url := 'https://rjgyfdwkocwklhyaqoqz.supabase.co/functions/v1/send-card-notification';

  -- Use pg_net to make HTTP request to edge function with error handling
  BEGIN
    SELECT net.http_post(
      url := webhook_url,
      headers := '{"Content-Type": "application/json"}'::jsonb,
      body := jsonb_build_object(
        'record', card_data,
        'table', TG_TABLE_NAME
      )
    ) INTO response_id;
    
    -- Log successful notification
    RAISE LOG 'Card notification sent successfully, response_id: %', response_id;
    
  EXCEPTION WHEN OTHERS THEN
    -- Log the error but don't fail the card creation
    RAISE LOG 'Failed to send card notification: %', SQLERRM;
    -- Continue with the card creation even if notification fails
  END;

  RETURN NEW;
END;
$$;