-- Create admin_prompts table for baseline prompt management
CREATE TABLE public.admin_prompts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  prompt_text TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.admin_prompts ENABLE ROW LEVEL SECURITY;

-- Create policies for admin prompts (public read, no user updates)
CREATE POLICY "Anyone can view active admin prompts" 
ON public.admin_prompts 
FOR SELECT 
USING (is_active = true);

-- Create session_likes table for session-based like tracking
CREATE TABLE public.session_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT NOT NULL,
  card_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(session_id, card_id)
);

-- Enable RLS
ALTER TABLE public.session_likes ENABLE ROW LEVEL SECURITY;

-- Create policies for session likes
CREATE POLICY "Anyone can view session likes" 
ON public.session_likes 
FOR SELECT 
USING (true);

CREATE POLICY "Anyone can insert session likes" 
ON public.session_likes 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Anyone can delete session likes" 
ON public.session_likes 
FOR DELETE 
USING (true);

-- Insert default baseline prompt
INSERT INTO public.admin_prompts (name, prompt_text, is_active)
VALUES (
  'baseline_character',
  'Create a vibrant, anime-style Pokemon character card illustration. The character should have: bright, saturated colors with cel-shading art style, large expressive eyes typical of anime characters, dynamic pose showing personality, clean background that complements the character, professional trading card quality artwork. Style: anime/manga art, Pokemon card aesthetic, high contrast lighting, detailed but not cluttered.',
  true
);

-- Create trigger for updating admin_prompts timestamps
CREATE TRIGGER update_admin_prompts_updated_at
BEFORE UPDATE ON public.admin_prompts
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();