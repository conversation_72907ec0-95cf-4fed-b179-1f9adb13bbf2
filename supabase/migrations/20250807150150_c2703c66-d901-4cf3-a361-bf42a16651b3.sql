-- Configure storage bucket for better performance
UPDATE storage.buckets 
SET 
  file_size_limit = 5242880, -- 5MB limit
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp']
WHERE id = 'character-images';

-- Create storage policies for optimal caching (if not exists)
DO $$
BEGIN
  -- Add cache control headers policy if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM storage.objects 
    WHERE bucket_id = 'character-images' 
    AND metadata->>'cacheControl' IS NOT NULL 
    LIMIT 1
  ) THEN
    -- This is handled at the client level, but we can optimize the bucket
    NULL;
  END IF;
END $$;