
-- Create user_cards table to store personal card collections
CREATE TABLE public.user_cards (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id TEXT NOT NULL, -- Using session ID since no auth is implemented
  name TEXT NOT NULL,
  backstory TEXT,
  signature TEXT,
  image_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create leaderboard_cards table for submitted cards
CREATE TABLE public.leaderboard_cards (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  backstory TEXT,
  signature TEXT,
  image_url TEXT NOT NULL,
  likes INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add indexes for better performance
CREATE INDEX idx_user_cards_session_id ON public.user_cards(session_id);
CREATE INDEX idx_leaderboard_cards_likes ON public.leaderboard_cards(likes DESC);
CREATE INDEX idx_leaderboard_cards_created_at ON public.leaderboard_cards(created_at DESC);

-- Enable Row Level Security (making tables public for now since no auth)
ALTER TABLE public.user_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leaderboard_cards ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for public access
CREATE POLICY "Anyone can view user cards" ON public.user_cards FOR SELECT USING (true);
CREATE POLICY "Anyone can insert user cards" ON public.user_cards FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can update user cards" ON public.user_cards FOR UPDATE USING (true);
CREATE POLICY "Anyone can delete user cards" ON public.user_cards FOR DELETE USING (true);

CREATE POLICY "Anyone can view leaderboard cards" ON public.leaderboard_cards FOR SELECT USING (true);
CREATE POLICY "Anyone can insert leaderboard cards" ON public.leaderboard_cards FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can update leaderboard cards" ON public.leaderboard_cards FOR UPDATE USING (true);
CREATE POLICY "Anyone can delete leaderboard cards" ON public.leaderboard_cards FOR DELETE USING (true);

-- Enable realtime for leaderboard updates
ALTER PUBLICATION supabase_realtime ADD TABLE public.leaderboard_cards;
