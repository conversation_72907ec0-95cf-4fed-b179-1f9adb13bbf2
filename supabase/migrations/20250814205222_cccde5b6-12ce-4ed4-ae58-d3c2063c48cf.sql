-- Fix security vulnerability in bulk_orders INSERT policy
-- Add data validation, constraints, and audit trail while preserving order functionality

-- First, let's add better constraints to validate data quality and prevent abuse
ALTER TABLE public.bulk_orders 
ADD CONSTRAINT valid_email_format CHECK (customer_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
ADD CONSTRAINT valid_phone_format CHECK (customer_phone IS NULL OR length(customer_phone) >= 10),
ADD CONSTRAINT reasonable_name_length CHECK (length(customer_name) BETWEEN 2 AND 100),
ADD CONSTRAINT reasonable_address_length CHECK (length(customer_address) BETWEEN 5 AND 200),
ADD CONSTRAINT valid_zip_format CHECK (length(customer_zip) BETWEEN 3 AND 12),
ADD CONSTRAINT valid_status CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
ADD CONSTRAINT positive_quantity CHECK (total_quantity > 0),
ADD CONSTRAINT reasonable_quantity CHECK (total_quantity <= 1000), -- Prevent bulk spam orders
ADD CONSTRAINT valid_amount CHECK (total_amount >= 0);

-- Add an audit trail column to track submission metadata
ALTER TABLE public.bulk_orders 
ADD COLUMN submission_ip inet,
ADD COLUMN submission_user_agent text,
ADD COLUMN submission_metadata jsonb DEFAULT '{}';

-- Create a more secure INSERT policy with additional checks
DROP POLICY IF EXISTS "Anyone can insert bulk orders" ON public.bulk_orders;

CREATE POLICY "Validated public order submissions only" 
ON public.bulk_orders 
FOR INSERT 
WITH CHECK (
  -- Ensure basic data quality
  customer_name IS NOT NULL AND 
  customer_email IS NOT NULL AND 
  customer_address IS NOT NULL AND 
  customer_city IS NOT NULL AND 
  customer_state IS NOT NULL AND 
  customer_zip IS NOT NULL AND 
  customer_country IS NOT NULL AND 
  order_items IS NOT NULL AND 
  total_quantity > 0 AND 
  total_quantity <= 100 AND -- Reasonable order size limit
  total_amount >= 0 AND
  status = 'pending' -- New orders must start as pending
);

-- Create a function to log order submissions (for audit trail)
CREATE OR REPLACE FUNCTION public.log_order_submission()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the submission for security monitoring
  INSERT INTO public.prompt_history (
    user_prompt, 
    processed_prompt, 
    generation_status,
    metadata,
    session_id
  ) VALUES (
    'Order submission logged',
    CONCAT('Order ID: ', NEW.id, ' - Customer: ', NEW.customer_email),
    'success',
    jsonb_build_object(
      'event_type', 'order_submission',
      'order_id', NEW.id,
      'customer_email_hash', encode(digest(NEW.customer_email, 'sha256'), 'hex'),
      'total_quantity', NEW.total_quantity,
      'total_amount', NEW.total_amount,
      'submission_time', NEW.created_at
    ),
    'audit_log'
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;