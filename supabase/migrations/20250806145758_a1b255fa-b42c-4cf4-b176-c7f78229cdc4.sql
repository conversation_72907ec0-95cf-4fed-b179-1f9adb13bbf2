-- Fix the security warning for the ensure_minimum_likes function
DROP FUNCTION IF EXISTS public.ensure_minimum_likes();

CREATE OR REPLACE FUNCTION public.ensure_minimum_likes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $function$
BEGIN
  -- If the card is more than a day old and has less than 5 likes, set minimum to 5
  IF NEW.created_at < (now() - interval '1 day') THEN
    NEW.likes = GREATEST(NEW.likes, 5);
    NEW.weekly_likes = GREATEST(NEW.weekly_likes, 5);
  END IF;
  
  RETURN NEW;
END;
$function$;