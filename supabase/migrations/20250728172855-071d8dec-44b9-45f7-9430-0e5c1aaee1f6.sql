-- Fix search_path security warnings for the new functions
CREATE OR REPLACE FUNCTION public.reset_weekly_likes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Reset all weekly likes to 0 and update week start date
  UPDATE public.leaderboard_cards 
  SET 
    weekly_likes = 0,
    week_start_date = date_trunc('week', now())::date;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_current_week_start()
RETURNS date
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN date_trunc('week', now())::date;
END;
$$;