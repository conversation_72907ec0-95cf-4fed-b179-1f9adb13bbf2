-- Add monthly tracking to leaderboard_cards
ALTER TABLE public.leaderboard_cards
  ADD COLUMN IF NOT EXISTS monthly_likes integer NOT NULL DEFAULT 0,
  ADD COLUMN IF NOT EXISTS month_start_date date NOT NULL DEFAULT date_trunc('month', now())::date;

-- Create monthly_winners table
CREATE TABLE IF NOT EXISTS public.monthly_winners (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  card_id uuid NOT NULL,
  month_start_date date NOT NULL,
  month_end_date date NOT NULL,
  final_likes integer NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now(),
  card_name text NOT NULL,
  card_creator text,
  card_location text
);

-- Enable RLS on monthly_winners and allow public SELECT
ALTER TABLE public.monthly_winners ENABLE ROW LEVEL SECURITY;
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'monthly_winners' AND policyname = 'Anyone can view monthly winners'
  ) THEN
    CREATE POLICY "Anyone can view monthly winners"
    ON public.monthly_winners
    FOR SELECT
    USING (true);
  END IF;
END $$;

-- Helper: current month start
CREATE OR REPLACE FUNCTION public.get_current_month_start()
RETURNS date
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  RETURN date_trunc('month', now())::date;
END;
$$;

-- Reset monthly likes function
CREATE OR REPLACE FUNCTION public.reset_monthly_likes()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
BEGIN
  UPDATE public.leaderboard_cards
  SET monthly_likes = 0,
      month_start_date = date_trunc('month', now())::date;
END;
$$;