-- Step 1: Fix all existing backstories over 150 characters
UPDATE public.user_cards 
SET backstory = LEFT(backstory, 147) || '...'
WHERE backstory IS NOT NULL AND LENGTH(backstory) > 150;

UPDATE public.leaderboard_cards 
SET backstory = LEFT(backstory, 147) || '...'
WHERE backstory IS NOT NULL AND LENGTH(backstory) > 150;

-- Step 2: Create function to truncate backstories
CREATE OR REPLACE FUNCTION public.truncate_backstory()
RETURNS TRIGGER AS $$
BEGIN
  -- If backstory is longer than 150 characters, truncate it
  IF NEW.backstory IS NOT NULL AND LENGTH(NEW.backstory) > 150 THEN
    NEW.backstory = LEFT(NEW.backstory, 147) || '...';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create triggers for both tables
CREATE TRIGGER truncate_backstory_user_cards
  BEFORE INSERT OR UPDATE ON public.user_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.truncate_backstory();

CREATE TRIGGER truncate_backstory_leaderboard_cards
  BEFORE INSERT OR UPDATE ON public.leaderboard_cards
  FOR EACH ROW
  EXECUTE FUNCTION public.truncate_backstory();