-- Fix security vulnerability in bulk_orders INSERT policy
-- Step 1: Clean up invalid data first, then add constraints

-- Clean up invalid phone numbers (set very short ones to NULL)
UPDATE public.bulk_orders 
SET customer_phone = NULL 
WHERE customer_phone IS NOT NULL AND length(customer_phone) < 10;

-- Now add the security constraints
ALTER TABLE public.bulk_orders 
ADD CONSTRAINT valid_email_format CHECK (customer_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
ADD CONSTRAINT valid_phone_format CHECK (customer_phone IS NULL OR length(customer_phone) >= 10),
ADD CONSTRAINT reasonable_name_length CHECK (length(customer_name) BETWEEN 2 AND 100),
ADD CONSTRAINT reasonable_address_length CHECK (length(customer_address) BETWEEN 5 AND 200),
ADD CONSTRAINT valid_zip_format CHECK (length(customer_zip) BETWEEN 3 AND 12),
ADD CONSTRAINT valid_status CHECK (status IN ('pending', 'processing', 'shipped', 'delivered', 'cancelled')),
ADD CONSTRAINT positive_quantity CHECK (total_quantity > 0),
ADD CONSTRAINT reasonable_quantity CHECK (total_quantity <= 1000),
ADD CONSTRAINT valid_amount CHECK (total_amount >= 0);

-- Add audit trail columns
ALTER TABLE public.bulk_orders 
ADD COLUMN submission_ip inet,
ADD COLUMN submission_user_agent text,
ADD COLUMN submission_metadata jsonb DEFAULT '{}';

-- Replace the overly permissive INSERT policy with a validated one
DROP POLICY IF EXISTS "Anyone can insert bulk orders" ON public.bulk_orders;

CREATE POLICY "Validated public order submissions only" 
ON public.bulk_orders 
FOR INSERT 
WITH CHECK (
  -- Data validation requirements
  customer_name IS NOT NULL AND 
  customer_email IS NOT NULL AND 
  customer_address IS NOT NULL AND 
  customer_city IS NOT NULL AND 
  customer_state IS NOT NULL AND 
  customer_zip IS NOT NULL AND 
  customer_country IS NOT NULL AND 
  order_items IS NOT NULL AND 
  total_quantity > 0 AND 
  total_quantity <= 100 AND -- Prevent abuse with massive orders
  total_amount >= 0 AND
  status = 'pending' -- Force new orders to start as pending
);

-- Create audit logging function
CREATE OR REPLACE FUNCTION public.log_order_submission()
RETURNS TRIGGER AS $$
BEGIN
  -- Create audit log entry (using existing prompt_history table)
  INSERT INTO public.prompt_history (
    user_prompt, 
    processed_prompt, 
    generation_status,
    metadata,
    session_id
  ) VALUES (
    'Order submission audit',
    CONCAT('Order: ', NEW.id),
    'success',
    jsonb_build_object(
      'event_type', 'order_submission',
      'order_id', NEW.id,
      'customer_email_hash', encode(digest(NEW.customer_email, 'sha256'), 'hex'),
      'total_quantity', NEW.total_quantity,
      'total_amount', NEW.total_amount,
      'submission_time', NEW.created_at
    ),
    'audit_log'
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Add the audit trigger
CREATE TRIGGER audit_order_submissions
  AFTER INSERT ON public.bulk_orders
  FOR EACH ROW
  EXECUTE FUNCTION public.log_order_submission();