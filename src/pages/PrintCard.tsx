import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { CharacterCard } from '@/components/CharacterCard';
import { useEffect } from 'react';

interface CardData {
  id: string;
  image_url: string;
  name: string;
  backstory?: string;
  image_description?: string;
  likes: number;
  user_id?: string;
  created_at: string;
}

const PrintCard = () => {
  const { cardId } = useParams<{ cardId: string }>();
  const navigate = useNavigate();

  const { data: card, isLoading, error } = useQuery({
    queryKey: ['print-card', cardId],
    queryFn: async () => {
      if (!cardId) throw new Error('Card ID is required');

      // Try leaderboard_cards first
      const { data: leaderboardCard, error: leaderboardError } = await supabase
        .from('leaderboard_cards')
        .select('*')
        .eq('id', cardId)
        .single();

      if (!leaderboardError && leaderboardCard) {
        return { ...leaderboardCard, source: 'leaderboard' };
      }

      // If not found in leaderboard, try user_cards
      const { data: userCard, error: userError } = await supabase
        .from('user_cards')
        .select('*')
        .eq('id', cardId)
        .single();

      if (!userError && userCard) {
        return { ...userCard, source: 'user' };
      }

      throw new Error('Card not found');
    },
    enabled: !!cardId,
  });

  useEffect(() => {
    if (!cardId) {
      navigate('/');
    }
  }, [cardId, navigate]);

  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'white',
        fontFamily: 'ui-sans-serif, system-ui, sans-serif'
      }}>
        <div style={{ textAlign: 'center', color: '#6b7280' }}>
          <div style={{ marginBottom: '16px', fontSize: '18px' }}>Loading card...</div>
        </div>
      </div>
    );
  }

  if (error || !card) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'white',
        fontFamily: 'ui-sans-serif, system-ui, sans-serif'
      }}>
        <div style={{ textAlign: 'center', color: '#ef4444' }}>
          <div style={{ marginBottom: '16px', fontSize: '18px' }}>Card not found</div>
          <div style={{ fontSize: '14px' }}>The requested card could not be loaded.</div>
        </div>
      </div>
    );
  }

  // Use superpower only; ignore legacy image_description
  const description = card.backstory && card.backstory.trim().length > 0
    ? card.backstory 
    : "This magical friend loves adventures and spreading joy! ✨🌈";

  // Create a fixed-size print card with bleed background
  const createPrintCard = () => {
    return (
      <div 
        id="print-card-container"
        style={{
          width: '1200px',
          height: '1500px',
          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 25%, #4facfe 50%, #00f2fe 75%, #43e97b 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '40px',
          position: 'relative'
        }}
      >
        {/* Print bleed background effect */}
        <div style={{
          position: 'absolute',
          inset: '0',
          background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0.1) 100%)',
          mixBlendMode: 'overlay'
        }}></div>
        
        {/* Card with fixed dimensions */}
        <div style={{
          width: '735px',
          height: '1030px',
          background: 'linear-gradient(135deg, #ec4899, #8b5cf6, #06b6d4)',
          borderRadius: '24px',
          padding: '8px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          position: 'relative'
        }}>
          {/* Sparkly border effect */}
          <div style={{
            position: 'absolute',
            inset: '0',
            background: 'linear-gradient(45deg, #fbbf24, #ec4899, #8b5cf6)',
            borderRadius: '24px',
            opacity: '0.75',
            filter: 'blur(4px)',
            animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
          }}></div>
          
          {/* Inner card */}
          <div style={{
            position: 'relative',
            background: 'linear-gradient(135deg, #fef3c7, #fce7f3, #dbeafe)',
            borderRadius: '16px',
            height: '100%',
            border: '4px solid white',
            boxShadow: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
            overflow: 'hidden'
          }}>
            {/* Header */}
            <div style={{
              background: 'linear-gradient(90deg, #ec4899, #8b5cf6, #3b82f6)',
              padding: '24px',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* Star dot pattern */}
              <div style={{
                position: 'absolute',
                inset: '0',
                opacity: '0.2',
                backgroundImage: 'radial-gradient(circle, white 2px, transparent 2px)',
                backgroundSize: '20px 20px'
              }}></div>
              <span style={{
                fontSize: '32px',
                fontWeight: '900',
                color: 'white',
                textShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                letterSpacing: '0.1em',
                position: 'relative',
                zIndex: '1'
              }}>PIXICARD</span>
            </div>

            {/* Content */}
            <div style={{ padding: '24px' }}>
              {/* Name */}
              <div style={{
                textAlign: 'center',
                marginBottom: '20px',
                background: 'linear-gradient(90deg, #fbbf24, #ec4899, #8b5cf6)',
                borderRadius: '16px',
                padding: '20px',
                border: '2px solid #fbbf24'
              }}>
                <h3 style={{
                  fontSize: '24px',
                  fontWeight: '900',
                  color: '#581c87',
                  margin: '0',
                  minHeight: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {card.name || "🌟 Magical Friend 🌟"}
                </h3>
              </div>

              {/* Character Image */}
              <div style={{
                marginBottom: '20px',
                borderRadius: '20px',
                overflow: 'hidden',
                background: 'linear-gradient(135deg, #f9a8d4, #93c5fd)',
                padding: '8px',
                border: '4px solid white',
                height: '420px'
              }}>
                <img 
                  src={card.image_url} 
                  alt={card.name || "Generated character"}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '12px'
                  }}
                />
              </div>

              {/* Story */}
              <div style={{
                background: 'linear-gradient(135deg, #ffffff, #dbeafe)',
                border: '2px solid #93c5fd',
                borderRadius: '16px',
                padding: '20px',
                marginBottom: '20px'
              }}>
                <p style={{
                  fontSize: '16px',
                  color: '#581c87',
                  textAlign: 'center',
                  lineHeight: '1.6',
                  background: 'white',
                  borderRadius: '12px',
                  padding: '16px',
                  border: '1px solid #c4b5fd',
                  margin: '0',
                  minHeight: '80px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {description || "This magical friend loves adventures and spreading joy! ✨🌈"}
                </p>
              </div>

              {/* Footer */}
              <div style={{
                background: 'linear-gradient(90deg, #8b5cf6, #ec4899, #f97316)',
                color: 'white',
                borderRadius: '16px',
                padding: '20px',
                textAlign: 'center'
              }}>
                <div style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                  lineHeight: '1.5',
                  minHeight: '40px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  ✨ Create your magical character card ✨
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const downloadAsPNG = async () => {
    const cardElement = document.getElementById('print-card-container');
    if (!cardElement) {
      console.error('Print card container not found');
      return;
    }

    // Store original positioning values
    const originalPosition = cardElement.style.position;
    const originalTop = cardElement.style.top;
    const originalLeft = cardElement.style.left;
    const originalZIndex = cardElement.style.zIndex;

    try {
      // Temporarily hide the element off-screen to prevent flash
      cardElement.style.position = 'fixed';
      cardElement.style.top = '-9999px';
      cardElement.style.left = '-9999px';
      cardElement.style.zIndex = '-1';

      const html2canvas = (await import('html2canvas')).default;
      
      // Convert the in-card image to a data URL to avoid CORS issues
      const imgEl = cardElement.querySelector('img') as HTMLImageElement | null;
      let originalSrc: string | null = null;
      if (imgEl && imgEl.src) {
        originalSrc = imgEl.src;
        const dataUrl = await new Promise<string>((resolve) => {
          const img = new Image();
          img.crossOrigin = 'anonymous';
          img.onload = () => {
            try {
              const c = document.createElement('canvas');
              c.width = img.naturalWidth;
              c.height = img.naturalHeight;
              const ctx = c.getContext('2d');
              if (!ctx) throw new Error('No canvas context');
              ctx.drawImage(img, 0, 0);
              resolve(c.toDataURL('image/png'));
            } catch (e) {
              console.warn('Failed to convert image to data URL, using original', e);
              resolve(originalSrc!);
            }
          };
          img.onerror = () => resolve(originalSrc!);
          img.src = originalSrc!;
        });
        imgEl.src = dataUrl;
        await new Promise<void>((r) => {
          if (imgEl.complete) return r();
          imgEl.onload = () => r();
          imgEl.onerror = () => r();
        });
      }
      
      // Capture the exact print card container with fixed size
      const canvas = await html2canvas(cardElement, {
        width: 1200,
        height: 1500,
        scale: 3,
        backgroundColor: null,
        useCORS: false,
        allowTaint: true,
        logging: false,
        foreignObjectRendering: false
      });

      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${card.name || 'pixicard'}-print-ready.png`;
          link.click();
          URL.revokeObjectURL(url);
        }
      }, 'image/png');

      // Restore original positioning
      cardElement.style.position = originalPosition;
      cardElement.style.top = originalTop;
      cardElement.style.left = originalLeft;
      cardElement.style.zIndex = originalZIndex;
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again or right-click the card and select "Save Image As"');
      
      // Restore original positioning even on error
      cardElement.style.position = originalPosition;
      cardElement.style.top = originalTop;
      cardElement.style.left = originalLeft;
      cardElement.style.zIndex = originalZIndex;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex justify-center items-center" style={{ fontFamily: 'ui-sans-serif, system-ui, sans-serif' }}>
      {/* Download button */}
      <div className="fixed top-5 right-5 z-50">
        <button
          onClick={downloadAsPNG}
          className="bg-gradient-to-r from-pink-500 to-purple-600 text-white font-bold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all"
        >
          Download as PNG
        </button>
      </div>

      {/* Print-ready card with bleed */}
      {createPrintCard()}
    </div>
  );
};

export default PrintCard;