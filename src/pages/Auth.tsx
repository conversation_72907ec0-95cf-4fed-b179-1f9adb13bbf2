import { useState, useEffect } from 'react';
import { Navigate, Link } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Home, Trophy, Plus, Heart } from 'lucide-react';
import { PlayingCardStar } from '@/components/ui/playing-card-star';
import { supabase } from '@/integrations/supabase/client';

export default function Auth() {
  const { user, signIn, signUp } = useAuth();
  const { toast } = useToast();
  const [isLogin, setIsLogin] = useState(true);
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isCheckEmail, setIsCheckEmail] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState('');

  useEffect(() => {
    document.title = isLogin ? 'Sign In | PixiCards' : 'Sign Up | PixiCards';
  }, [isLogin]);

  // Redirect if already logged in
  const params = new URLSearchParams(window.location.search);
  const redirectTarget = params.get('redirect') || localStorage.getItem('postAuthRedirect') || '/';
  if (user) {
    try { localStorage.removeItem('postAuthRedirect'); } catch {}
    return <Navigate to={redirectTarget} replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      let result;
      if (isLogin) {
        result = await signIn(email, password);
      } else {
        result = await signUp(email, password, fullName);
      }

      if (result.error) {
        toast({
          title: "Error",
          description: result.error.message,
          variant: "destructive"
        });
      } else if (!isLogin) {
        setSubmittedEmail(email);
        setIsCheckEmail(true);
        setPassword('');
        // Notify admin about a new account signup (fire-and-forget)
        supabase.functions.invoke('notify-admin', {
          body: { event: 'account_created', email }
        }).catch(() => {});
        toast({
          title: "Confirmation sent",
          description: "Check your email and click the link to finish signing up.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  const handleResend = async () => {
    if (!submittedEmail) return;
    setLoading(true);
    try {
      const redirectUrl = `${window.location.origin}/`;
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: submittedEmail,
        options: { emailRedirectTo: redirectUrl }
      } as any);
      if (error) throw error;
      toast({ title: 'Email re-sent', description: 'Check your inbox (and spam) for the confirmation link.' });
    } catch (err: any) {
      toast({ title: 'Could not resend', description: err?.message || 'Please try again later.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <nav className="fixed top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border shadow-lg w-full">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <PlayingCardStar className="w-6 h-6 sm:w-8 sm:h-8 text-accent mr-2 sm:mr-3" />
              <span className="text-lg sm:text-2xl font-bold bg-hero-gradient bg-clip-text text-transparent">PIXICARDS</span>
            </div>
            <div className="flex gap-2 sm:gap-3 items-center">
              <Button asChild variant="outline" className="rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] hover:scale-105">
                <Link to="/">
                  <Home className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Home</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] hover:scale-105">
                <Link to="/">
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Create</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] hover:scale-105">
                <Link to="/">
                  <Trophy className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">Vote</span>
                </Link>
              </Button>
              <Button asChild variant="outline" className="rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] hover:scale-105">
                <Link to="/">
                  <Heart className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">My Collection</span>
                </Link>
              </Button>
              <Button asChild className="rounded-full text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:scale-105 transition-all">
                <Link to="/order">
                  <span className="hidden sm:inline">Order</span>
                  <span className="sm:hidden">🛒</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-blue-600 flex items-center justify-center p-4 pt-24 relative">
        <div className="absolute inset-0 bg-black/20"></div>

        {isCheckEmail ? (
          <Card className="w-full max-w-md relative z-10 bg-white/95 backdrop-blur-sm border-0 shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              Check your email
            </CardTitle>
            <CardDescription>
              We sent a confirmation link to {submittedEmail}. Click it to finish signing up.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              onClick={handleResend}
              disabled={loading}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              {loading ? 'Resending...' : 'Resend email'}
            </Button>
            <Button
              variant="outline"
              onClick={() => { setIsLogin(true); setIsCheckEmail(false); }}
              className="w-full"
            >
              Back to Sign In
            </Button>
            <Button asChild variant="link" className="w-full mt-2">
              <Link to="/">Back to main site</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card className="w-full max-w-md relative z-10 bg-white/95 backdrop-blur-sm border-0 shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              PIXICARDS
            </CardTitle>
            <CardDescription>
              {isLogin ? 'Welcome back!' : 'Create your magical account'}
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {!isLogin && (
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Enter your full name"
                    required={!isLogin}
                  />
                </div>
              )}
              
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your password"
                  required
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                disabled={loading}
              >
                {loading ? 'Please wait...' : (isLogin ? 'Sign In' : 'Sign Up')}
              </Button>
            </form>
            
            <div className="text-center mt-4">
              <button
                type="button"
                onClick={() => setIsLogin(!isLogin)}
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                {isLogin 
                  ? "Don't have an account? Sign up" 
                  : "Already have an account? Sign in"
                }
              </button>
            </div>
            <div className="text-center mt-2">
              <Button asChild variant="link">
                <Link to="/">Back to main site</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      </div>
    </>
  );
}