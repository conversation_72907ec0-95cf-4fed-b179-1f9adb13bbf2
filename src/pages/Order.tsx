import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, ShoppingCart, Home, Trophy, Plus, ArrowLeft } from "lucide-react";
import { OrderCard } from "@/components/OrderCard";
import { OrderSummary, OrderItem } from "@/components/OrderSummary";
import { PrintOrderForm } from "@/components/PrintOrderForm";
import { OrderSelection } from "@/components/OrderSelection";
import { PackOrderForm } from "@/components/PackOrderForm";
import { BinderOrderForm } from "@/components/BinderOrderForm";
import { supabase } from "@/integrations/supabase/client";
import confetti from "canvas-confetti";
import { useToast } from "@/hooks/use-toast";
import { PlayingCardStar } from "@/components/ui/playing-card-star";

interface Card {
  id: string;
  name: string;
  image_url: string;
  user_id?: string;
  signature?: string;
  creator?: string;
}

export default function Order() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [cards, setCards] = useState<Card[]>([]);
  const [filteredCards, setFilteredCards] = useState<Card[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [currentStep, setCurrentStep] = useState<'selection' | 'browse' | 'checkout' | 'pack-checkout' | 'binder-checkout' | 'success'>('selection');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const stepParam = params.get('step') as any;
    if (stepParam === 'pack-checkout' || stepParam === 'checkout' || stepParam === 'browse' || stepParam === 'binder-checkout') {
      setCurrentStep(stepParam);
    }
  }, []);

  // React to Stripe redirect status
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const status = params.get('status');
    if (status === 'success') {
      handleOrderSubmitted();
      const sessionId = params.get('session_id') || undefined;
      if (sessionId) {
        // Send lightweight admin notification (best-effort)
        supabase.functions.invoke('notify-admin', {
          body: { event: 'order_completed', session_id: sessionId }
        }).catch(() => {});
        // Verify checkout with Stripe and send email reliably (works for 0-amount orders)
        supabase.functions.invoke('verify-checkout', {
          body: { session_id: sessionId }
        }).catch(() => {});
      }
    } else if (status === 'cancelled') {
      toast({ title: 'Checkout canceled', description: 'You can resume anytime.' });
    }
  }, [toast]);

  // Fetch all cards
  useEffect(() => {
    const fetchCards = async () => {
      try {
        // Get leaderboard cards
        const { data: leaderboardCards, error: leaderboardError } = await supabase
          .from('leaderboard_cards')
          .select('id, name, image_url, user_id, signature')
          .order('likes', { ascending: false });

        // Get user cards
        const { data: userCards, error: userError } = await supabase
          .from('user_cards')
          .select('id, name, image_url, user_id, signature')
          .order('created_at', { ascending: false });

        if (leaderboardError) throw leaderboardError;
        if (userError) throw userError;

        // Helper function to detect auto-generated signatures
        const isAutoGeneratedSignature = (signature?: string) => {
          if (!signature) return true;
          // Check for "Created DD/MM/YYYY at HH:MM" pattern
          return /^Created \d{2}\/\d{2}\/\d{4} at \d{2}:\d{2}$/.test(signature);
        };

        // Helper function to get proper creator name
        const getCreatorName = (signature?: string) => {
          if (!signature || isAutoGeneratedSignature(signature)) {
            return 'Unknown Creator';
          }
          return signature;
        };

        // Combine and format cards with filtering for quality
        const allCards: Card[] = [
          // Prioritize leaderboard cards (better quality)
          ...(leaderboardCards?.filter(card => 
            card.name && 
            card.name.trim() !== '' && 
            card.image_url
          ).map(card => ({
            ...card,
            creator: getCreatorName(card.signature)
          })) || []),
          // Add user cards that meet quality standards
          ...(userCards?.filter(card => 
            card.name && 
            card.name.trim() !== '' && 
            card.image_url
          ).map(card => ({
            ...card,
            creator: getCreatorName(card.signature)
          })) || [])
        ];

        // Sort by quality: cards with real signatures first, then by likes/creation date
        allCards.sort((a, b) => {
          const aHasRealSignature = !isAutoGeneratedSignature(a.signature);
          const bHasRealSignature = !isAutoGeneratedSignature(b.signature);
          
          if (aHasRealSignature && !bHasRealSignature) return -1;
          if (!aHasRealSignature && bHasRealSignature) return 1;
          
          return 0; // Keep original order within each quality tier
        });

        setCards(allCards);
        setFilteredCards(allCards);
      } catch (error) {
        console.error('Error fetching cards:', error);
        toast({
          title: "Error",
          description: "Failed to load cards. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCards();
  }, [toast]);

  // Filter cards based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCards(cards);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = cards.filter(card => 
        card.name.toLowerCase().includes(query) ||
        (card.creator && card.creator.toLowerCase().includes(query))
      );
      setFilteredCards(filtered);
    }
  }, [searchQuery, cards]);

  const handleAddCard = (card: { id: string; name: string; creator: string; imageUrl: string }) => {
    const existingItem = orderItems.find(item => item.id === card.id);
    
    if (existingItem) {
      // Add another batch of 10
      setOrderItems(prev => 
        prev.map(item => 
          item.id === card.id 
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
      toast({
        title: "Batch Added",
        description: `Added another batch of 10 ${card.name} cards to your order!`,
      });
    } else {
      // Add new item with 1 batch (10 cards)
      setOrderItems(prev => [...prev, {
        id: card.id,
        name: card.name,
        creator: card.creator,
        imageUrl: card.imageUrl,
        quantity: 1
      }]);

      toast({
        title: "Cards Added",
        description: `10 ${card.name} cards added to your order!`,
      });
    }
  };

  const handleUpdateQuantity = (id: string, quantity: number) => {
    setOrderItems(prev => 
      prev.map(item => 
        item.id === id ? { ...item, quantity } : item
      )
    );
  };

  const handleRemoveItem = (id: string) => {
    setOrderItems(prev => prev.filter(item => item.id !== id));
  };

  const handleOrderSubmitted = () => {
    // Trigger confetti
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });

    setCurrentStep('success');
  };

  const totalCards = orderItems.reduce((sum, item) => sum + (item.quantity * 10), 0);

  const renderSuccessPage = () => (
    <div className="space-y-8">
      <div className="text-center">
        <div className="text-6xl mb-4">🎉</div>
        <h1 className="text-3xl font-bold bg-hero-gradient bg-clip-text text-transparent mb-4">Order Complete!</h1>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          Woohoo! Your purchase is confirmed. We're buzzing with excitement and already getting your PixiCards ready. We'll be in touch soon with shipping details and tracking.
        </p>
        <div className="bg-accent/10 border border-accent/20 rounded-lg p-4 mb-6 max-w-sm mx-auto">
          <p className="text-sm text-accent font-medium">
            🚀 What's next?
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Keep an eye on your inbox for your receipt and shipment updates.
          </p>
        </div>
        <Button onClick={() => navigate('/')} className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-8 py-4 rounded-full shadow-magical">
          Return Home
        </Button>
      </div>
    </div>
  );

  const renderSelectionPage = () => (
    <OrderSelection 
      onSelectIndividual={() => setCurrentStep('browse')}
      onSelectPacks={() => setCurrentStep('pack-checkout')}
      onSelectBinders={() => setCurrentStep('binder-checkout')}
    />
  );

  const renderPackCheckout = () => (
    <PackOrderForm 
      onOrderSubmitted={handleOrderSubmitted}
      onBack={() => setCurrentStep('selection')}
    />
  );

  const renderBinderCheckout = () => (
    <BinderOrderForm 
      onOrderSubmitted={handleOrderSubmitted}
      onBack={() => setCurrentStep('selection')}
    />
  );

  const renderOrderPage = () => (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl sm:text-4xl font-bold bg-hero-gradient bg-clip-text text-transparent flex items-center justify-center gap-2">
          <ShoppingCart className="w-8 h-8 text-accent" />
          Print Your PixiCards 🎴
        </h1>
        <p className="text-muted-foreground mb-4">Transform your digital PixiCards into stunning physical collectibles! Each design ordered in batches of 10. £10 for the first 10, then £5 for each additional 10 of the same design.</p>
        <div className="max-w-2xl mx-auto p-3 bg-accent/10 border border-accent/20 rounded-lg">
          <p className="text-sm font-medium text-accent flex items-center justify-center gap-2">
            🎁 Giving as a gift? 
            <span className="font-normal text-muted-foreground">
              Just enter the recipient's shipping details at checkout for direct delivery!
            </span>
          </p>
        </div>
      </div>

      {currentStep === 'browse' && (
        <>
          {/* Back Button */}
          <div className="mb-4">
            <Button 
              variant="ghost" 
              onClick={() => setCurrentStep('selection')}
              className="hover:scale-105 transition-bounce"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Order Options
            </Button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Card Browser */}
            <div className="lg:col-span-2">
              <div className="bg-card border rounded-lg p-4 shadow-lg">
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                    <Input
                      placeholder="Search by character name or creator..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {isLoading ? (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground">Loading cards...</div>
                  </div>
                ) : filteredCards.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="text-muted-foreground">
                      {searchQuery ? 'No cards found matching your search.' : 'No cards available.'}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {filteredCards.map((card) => (
                      <OrderCard
                        key={card.id}
                        id={card.id}
                        imageUrl={card.image_url}
                        name={card.name}
                        creator={card.creator}
                        onAdd={handleAddCard}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Order Summary */}
            <div>
              <OrderSummary
                items={orderItems}
                onUpdateQuantity={handleUpdateQuantity}
                onRemoveItem={handleRemoveItem}
                showPricing={true}
              />
              {orderItems.length > 0 && (
                <div className="mt-4">
                  <Button 
                    onClick={() => setCurrentStep('checkout')}
                    className="w-full bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold py-4 rounded-full shadow-magical"
                  >
                    Checkout ({totalCards} cards)
                  </Button>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {currentStep === 'checkout' && (
        <div className="max-w-2xl mx-auto space-y-6">
          <Button 
            variant="ghost" 
            onClick={() => setCurrentStep('browse')}
            className="hover:scale-105 transition-bounce"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Card Selection
          </Button>
          
          <OrderSummary
            items={orderItems}
            onUpdateQuantity={handleUpdateQuantity}
            onRemoveItem={handleRemoveItem}
            showPricing={true}
          />

          <PrintOrderForm 
            orderItems={orderItems}
            onOrderSubmitted={handleOrderSubmitted}
          />
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation - Same as main site */}
      <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur-lg border-b border-border shadow-lg">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <PlayingCardStar className="w-6 h-6 sm:w-8 sm:h-8 text-accent mr-2 sm:mr-3" />
              <span className="text-lg sm:text-2xl font-bold bg-hero-gradient bg-clip-text text-transparent">PIXICARDS</span>
            </div>
            
            <div className="flex gap-2 sm:gap-3 items-center">
              {[
                { href: '/', icon: Home, label: 'Home' },
                { href: '/', icon: Plus, label: 'Create' },
                { href: '/', icon: Trophy, label: 'Vote' }
              ].map(({ href, icon: Icon, label }) => (
                <Button
                  key={label}
                  variant="outline"
                  onClick={() => navigate(href)}
                  className={`rounded-full transition-bounce text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] min-w-[40px] hover:scale-105`}
                >
                  <Icon className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-1" />
                  <span className="hidden sm:inline">{label}</span>
                </Button>
              ))}
              
              <Button
                variant="default"
                className="rounded-full text-xs sm:text-sm px-3 py-2 sm:px-4 sm:py-2 min-h-[40px] bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:scale-105 transition-all"
              >
                <span className="hidden sm:inline">Order</span>
                <span className="sm:hidden">🛒</span>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        {currentStep === 'success' ? renderSuccessPage() : 
         currentStep === 'selection' ? renderSelectionPage() :
         currentStep === 'pack-checkout' ? renderPackCheckout() : 
         currentStep === 'binder-checkout' ? renderBinderCheckout() :
         renderOrderPage()}
      </main>

      {/* Footer */}
      <footer className="text-center py-6">
        <p className="text-xs text-muted-foreground">
          Contact <NAME_EMAIL>
        </p>
      </footer>

      {/* Background Elements - Same as main site */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary/5 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-bounce-gentle"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-secondary/3 rounded-full blur-3xl animate-pulse"></div>
      </div>
    </div>
  );
}