import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import { AuthProvider } from './hooks/useAuth.tsx'
import './index.css'

const APP_VERSION = '2025-08-09-01'

const mount = () => {
  createRoot(document.getElementById("root")!).render(
    <AuthProvider>
      <App />
    </AuthProvider>
  );
}

// Version guard to bust stubborn Safari caches and old templates
try {
  const KEY = 'app_version'
  const stored = localStorage.getItem(KEY)
  if (stored !== APP_VERSION) {
    localStorage.setItem(KEY, APP_VERSION)

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then((regs) => regs.forEach((r) => r.unregister()))
    }
    if ('caches' in window) {
      caches.keys().then((keys) => keys.forEach((k) => caches.delete(k)))
    }

    const url = new URL(window.location.href)
    url.searchParams.set('v', APP_VERSION)
    window.location.replace(url.toString())
  } else {
    mount()
  }
} catch {
  // Fallback: if anything goes wrong, just mount the app
  mount()
}
