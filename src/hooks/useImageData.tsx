import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { queryLeaderboardCards } from '@/lib/cardQueries';

export interface LeaderboardCard {
  id: string;
  name: string;
  image_url: string;
  backstory?: string;
  signature?: string;
  likes: number;
  monthly_likes: number;
  level: number;
  created_at: string;
  month_start_date: string;
  power_stat?: number;
  magic_stat?: number;
  speed_stat?: number;
}

export interface MonthlyWinner {
  id: string;
  card_id: string;
  month_start_date: string;
  month_end_date: string;
  final_likes: number;
  card_name: string;
  card_creator?: string;
  card_location?: string;
  card_data?: LeaderboardCard;
}

// Centralized data fetching with React Query
export const useLeaderboardCards = () => {
  return useQuery({
    queryKey: ['leaderboard-cards'],
    queryFn: async () => {
      const { data, error } = await queryLeaderboardCards({
        order: { column: 'created_at', ascending: false }
      });
      if (error) throw error;
      return data as LeaderboardCard[];
    },
    staleTime: 1 * 60 * 1000, // 1 minute (reduced for quicker updates)
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useMonthlyWinner = () => {
  return useQuery({
    queryKey: ['monthly-winner'],
    queryFn: async () => {
      const { data: winnerData, error: winnerError } = await supabase
        .from('monthly_winners')
        .select('*')
        .order('month_end_date', { ascending: false })
        .limit(1);
      
      if (winnerError) throw winnerError;
      
      if (!winnerData || winnerData.length === 0) return null;
      
      const winner = winnerData[0];
      
      // Get the actual card data
      const { data: cardData, error: cardError } = await supabase
        .from('leaderboard_cards')
        .select('*')
        .eq('id', winner.card_id)
        .single();
      
      if (cardError) throw cardError;
      
      return {
        ...winner,
        card_data: cardData
      } as MonthlyWinner;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
};

export const useFeaturedCards = () => {
  return useQuery({
    queryKey: ['featured-cards'],
    queryFn: async () => {
      const { data, error } = await queryLeaderboardCards({
        select: 'id, name, image_url',
        order: { column: 'likes', ascending: false },
        limit: 6
      });
      if (error) throw error;
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useAllCardsForDisplay = () => {
  return useQuery({
    queryKey: ['all-display-cards'],
    queryFn: async () => {
      const [leaderboardResult, userCardsResult] = await Promise.all([
        queryLeaderboardCards({
          select: 'id, name, image_url',
          order: { column: 'created_at', ascending: false }
        }),
        supabase
          .from('user_cards')
          .select('id, name, image_url')
          .order('created_at', { ascending: false })
      ]);
      
      if (leaderboardResult.error) throw leaderboardResult.error;
      if (userCardsResult.error) throw userCardsResult.error;
      
      return [
        ...(leaderboardResult.data || []),
        ...(userCardsResult.data || [])
      ];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};