import { supabase } from '@/integrations/supabase/client';

/**
 * Safe query helper for leaderboard cards that respects RLS policies
 */
export async function queryLeaderboardCards({
  select = '*',
  order = { column: 'created_at', ascending: false },
  limit,
  includeSignatures = false
}: {
  select?: string;
  order?: { column: string; ascending: boolean };
  limit?: number;
  includeSignatures?: boolean;
} = {}) {
  try {
    // If signatures are needed, we need to be more specific about the select
    let selectClause: string;
    
    if (includeSignatures && select === '*') {
      // Try to get full data (will only work for own cards or if admin)
      selectClause = '*';
    } else if (includeSignatures) {
      selectClause = select;
    } else {
      // Exclude signature field to avoid RLS issues for public access
      if (select === '*') {
        selectClause = 'id, name, backstory, image_url, image_description, likes, created_at, updated_at, user_id, level, weekly_likes, week_start_date, power_stat, magic_stat, speed_stat, monthly_likes, month_start_date';
      } else {
        selectClause = select.replace(/,?\s*signature\s*,?/g, '').replace(/^,|,$/, '');
      }
    }
    
    let query = supabase.from('leaderboard_cards').select(selectClause);
    
    query = query.order(order.column, { ascending: order.ascending });
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error('Error querying leaderboard cards:', error);
      return { data: null, error };
    }
    
    return { data, error: null };
  } catch (error) {
    console.error('Error in queryLeaderboardCards:', error);
    return { data: null, error };
  }
}

/**
 * Get a single card with signature if user is authorized
 */
export async function getCardWithSignature(cardId: string) {
  try {
    const { data, error } = await supabase
      .from('leaderboard_cards')
      .select('*')
      .eq('id', cardId)
      .single();
    
    return { data, error };
  } catch (error) {
    console.error('Error getting card with signature:', error);
    return { data: null, error };
  }
}

/**
 * Get cards for authenticated user (includes their signatures)
 */
export async function getUserCards(userId: string) {
  try {
    const { data, error } = await supabase
      .from('leaderboard_cards')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    return { data, error };
  } catch (error) {
    console.error('Error getting user cards:', error);
    return { data: null, error };
  }
}