/**
 * Security utilities for input validation and sanitization
 */

/**
 * Sanitizes text input to prevent XSS attacks
 */
export function sanitizeText(input: string | null | undefined): string {
  if (!input) return '';
  
  // Create a temporary element to decode HTML entities safely
  const tempDiv = document.createElement('div');
  tempDiv.textContent = input;
  
  // Get the decoded text and remove any remaining HTML tags
  const decoded = tempDiv.textContent || '';
  
  // Remove any remaining script tags or dangerous content
  return decoded.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '');
}

/**
 * Validates email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
}

/**
 * Validates that a string doesn't contain suspicious patterns
 */
export function containsSuspiciousContent(input: string): boolean {
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /data:text\/html/i,
    /vbscript:/i,
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /<link/i,
    /<meta/i
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(input));
}

/**
 * Rate limiting helper - simple in-memory store
 * In production, use Redis or similar
 */
class SimpleRateLimit {
  private attempts: Map<string, { count: number; resetTime: number }> = new Map();
  
  isAllowed(key: string, maxAttempts: number = 5, windowMs: number = 300000): boolean {
    const now = Date.now();
    const record = this.attempts.get(key);
    
    if (!record || now > record.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }
    
    if (record.count >= maxAttempts) {
      return false;
    }
    
    record.count++;
    return true;
  }
  
  clear(key: string): void {
    this.attempts.delete(key);
  }
}

export const rateLimiter = new SimpleRateLimit();

/**
 * Content Security Policy nonce generator
 */
export function generateCSPNonce(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}