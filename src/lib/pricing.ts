// Shared pricing utilities for PixiCards orders
// Batch model: 10 cards per batch
// Pricing: £10 for the first 10 cards (first batch), then £5 for each additional batch of 10 of the same design

export const calculateItemPrice = (batches: number): number => {
  const qty = Math.max(0, Math.floor(batches));
  if (qty === 0) return 0;
  return 10 + Math.max(0, qty - 1) * 5;
};

export const calculateOrderTotal = (items: { quantity: number }[]): number => {
  return items.reduce((sum, item) => sum + calculateItemPrice(item.quantity), 0);
};

export const formatPrice = (price: number): string => `£${price.toFixed(2)}`;
