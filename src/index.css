@tailwind base;
@tailwind components;
@tailwind utilities;

/* Kid-friendly card creation app design system */

@layer base {
  :root {
    /* Magical backgrounds */
    --background: 240 100% 98%;
    --foreground: 240 50% 15%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 240 50% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 50% 15%;

    /* Bright, playful primaries - magical blue */
    --primary: 245 100% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 245 100% 75%;

    /* Warm secondary - sunshine orange */
    --secondary: 35 100% 65%;
    --secondary-foreground: 0 0% 100%;

    /* Soft muted colors */
    --muted: 240 20% 95%;
    --muted-foreground: 240 30% 50%;

    /* Accent - magical purple */
    --accent: 280 100% 70%;
    --accent-foreground: 0 0% 100%;

    /* Fun destructive - coral pink */
    --destructive: 350 100% 65%;
    --destructive-foreground: 0 0% 100%;

    /* Soft borders */
    --border: 240 20% 90%;
    --input: 240 20% 95%;
    --ring: 245 100% 60%;

    --radius: 1rem;

    /* Card magic */
    --card-gradient: linear-gradient(135deg, hsl(245 100% 95%), hsl(280 100% 95%));
    --hero-gradient: linear-gradient(135deg, hsl(245 100% 60%), hsl(280 100% 70%));
    --glow-gradient: linear-gradient(135deg, hsl(245 100% 75%), hsl(280 100% 80%));
    
    /* Magical shadows */
    --shadow-magical: 0 10px 30px -5px hsl(245 100% 60% / 0.3);
    --shadow-card: 0 8px 25px -5px hsl(245 50% 50% / 0.2);
    --shadow-glow: 0 0 40px hsl(245 100% 75% / 0.4);
    
    /* Smooth animations */
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Mobile touch improvements */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    /* Enable smooth scrolling */
    scroll-behavior: smooth;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  /* Allow text selection for input elements */
  input, textarea, [contenteditable] {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Improve tap targets for mobile */
  button, [role="button"], a {
    touch-action: manipulation;
    min-height: 44px;
    min-width: 44px;
  }

  /* Remove focus outline on mobile for better UX */
  @media (hover: none) and (pointer: coarse) {
    *:focus {
      outline: none;
    }
  }

  /* Global scale down for overall UI */
  html { font-size: 80%; }
}

/* Theatrical Winner Animations */
@keyframes swirl-in {
  0% {
    transform: translateX(100vw) rotate(720deg) scale(0.3);
    opacity: 0;
  }
  60% {
    transform: translateX(-20px) rotate(0deg) scale(1.1);
    opacity: 1;
  }
  80% {
    transform: translateX(10px) rotate(-10deg) scale(0.95);
  }
  100% {
    transform: translateX(0) rotate(0deg) scale(1);
    opacity: 1;
  }
}

@keyframes bounce-in-top {
  0% {
    transform: translateY(-500px) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translateY(30px) scale(1.1);
    opacity: 1;
  }
  80% {
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes bounce-text {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-20px) scale(1.05);
  }
  60% {
    transform: translateY(-10px) scale(1.02);
  }
}

@keyframes heart-beat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}