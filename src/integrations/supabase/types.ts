export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      admin_prompts: {
        Row: {
          created_at: string
          id: string
          is_active: boolean
          name: string
          prompt_text: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_active?: boolean
          name: string
          prompt_text: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          is_active?: boolean
          name?: string
          prompt_text?: string
          updated_at?: string
        }
        Relationships: []
      }
      bulk_orders: {
        Row: {
          created_at: string
          customer_address: string
          customer_city: string
          customer_country: string
          customer_email: string
          customer_name: string
          customer_phone: string | null
          customer_state: string
          customer_zip: string
          id: string
          order_items: Json
          special_instructions: string | null
          status: string
          submission_ip: unknown | null
          submission_metadata: Json | null
          submission_user_agent: string | null
          total_amount: number | null
          total_quantity: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          customer_address: string
          customer_city: string
          customer_country?: string
          customer_email: string
          customer_name: string
          customer_phone?: string | null
          customer_state: string
          customer_zip: string
          id?: string
          order_items: Json
          special_instructions?: string | null
          status?: string
          submission_ip?: unknown | null
          submission_metadata?: Json | null
          submission_user_agent?: string | null
          total_amount?: number | null
          total_quantity: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          customer_address?: string
          customer_city?: string
          customer_country?: string
          customer_email?: string
          customer_name?: string
          customer_phone?: string | null
          customer_state?: string
          customer_zip?: string
          id?: string
          order_items?: Json
          special_instructions?: string | null
          status?: string
          submission_ip?: unknown | null
          submission_metadata?: Json | null
          submission_user_agent?: string | null
          total_amount?: number | null
          total_quantity?: number
          updated_at?: string
        }
        Relationships: []
      }
      image_generation_settings: {
        Row: {
          active_provider: string
          created_at: string
          dalle_settings: Json
          gemini_settings: Json
          id: string
          updated_at: string
        }
        Insert: {
          active_provider?: string
          created_at?: string
          dalle_settings?: Json
          gemini_settings?: Json
          id?: string
          updated_at?: string
        }
        Update: {
          active_provider?: string
          created_at?: string
          dalle_settings?: Json
          gemini_settings?: Json
          id?: string
          updated_at?: string
        }
        Relationships: []
      }
      leaderboard_cards: {
        Row: {
          backstory: string | null
          created_at: string
          id: string
          image_description: string | null
          image_url: string
          level: number
          likes: number
          magic_stat: number | null
          month_start_date: string
          monthly_likes: number
          name: string
          power_stat: number | null
          signature: string | null
          speed_stat: number | null
          updated_at: string
          user_id: string | null
          week_start_date: string
          weekly_likes: number
        }
        Insert: {
          backstory?: string | null
          created_at?: string
          id?: string
          image_description?: string | null
          image_url: string
          level?: number
          likes?: number
          magic_stat?: number | null
          month_start_date?: string
          monthly_likes?: number
          name: string
          power_stat?: number | null
          signature?: string | null
          speed_stat?: number | null
          updated_at?: string
          user_id?: string | null
          week_start_date?: string
          weekly_likes?: number
        }
        Update: {
          backstory?: string | null
          created_at?: string
          id?: string
          image_description?: string | null
          image_url?: string
          level?: number
          likes?: number
          magic_stat?: number | null
          month_start_date?: string
          monthly_likes?: number
          name?: string
          power_stat?: number | null
          signature?: string | null
          speed_stat?: number | null
          updated_at?: string
          user_id?: string | null
          week_start_date?: string
          weekly_likes?: number
        }
        Relationships: []
      }
      monthly_winners: {
        Row: {
          card_creator: string | null
          card_id: string
          card_location: string | null
          card_name: string
          created_at: string
          final_likes: number
          id: string
          month_end_date: string
          month_start_date: string
        }
        Insert: {
          card_creator?: string | null
          card_id: string
          card_location?: string | null
          card_name: string
          created_at?: string
          final_likes: number
          id?: string
          month_end_date: string
          month_start_date: string
        }
        Update: {
          card_creator?: string | null
          card_id?: string
          card_location?: string | null
          card_name?: string
          created_at?: string
          final_likes?: number
          id?: string
          month_end_date?: string
          month_start_date?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          full_name: string | null
          id: string
          updated_at: string
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          full_name?: string | null
          id: string
          updated_at?: string
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          full_name?: string | null
          id?: string
          updated_at?: string
          username?: string | null
        }
        Relationships: []
      }
      prompt_history: {
        Row: {
          card_id: string | null
          created_at: string
          dalle_revised_prompt: string | null
          error_message: string | null
          generation_status: string
          id: string
          image_quality: string | null
          image_url: string | null
          metadata: Json | null
          processed_prompt: string
          session_id: string | null
          updated_at: string
          user_id: string | null
          user_prompt: string
        }
        Insert: {
          card_id?: string | null
          created_at?: string
          dalle_revised_prompt?: string | null
          error_message?: string | null
          generation_status?: string
          id?: string
          image_quality?: string | null
          image_url?: string | null
          metadata?: Json | null
          processed_prompt: string
          session_id?: string | null
          updated_at?: string
          user_id?: string | null
          user_prompt: string
        }
        Update: {
          card_id?: string | null
          created_at?: string
          dalle_revised_prompt?: string | null
          error_message?: string | null
          generation_status?: string
          id?: string
          image_quality?: string | null
          image_url?: string | null
          metadata?: Json | null
          processed_prompt?: string
          session_id?: string | null
          updated_at?: string
          user_id?: string | null
          user_prompt?: string
        }
        Relationships: []
      }
      session_likes: {
        Row: {
          card_id: string
          created_at: string
          id: string
          session_id: string
        }
        Insert: {
          card_id: string
          created_at?: string
          id?: string
          session_id: string
        }
        Update: {
          card_id?: string
          created_at?: string
          id?: string
          session_id?: string
        }
        Relationships: []
      }
      user_cards: {
        Row: {
          backstory: string | null
          created_at: string
          id: string
          image_description: string | null
          image_url: string
          level: number
          magic_stat: number | null
          name: string
          power_stat: number | null
          session_id: string | null
          signature: string | null
          speed_stat: number | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          backstory?: string | null
          created_at?: string
          id?: string
          image_description?: string | null
          image_url: string
          level?: number
          magic_stat?: number | null
          name: string
          power_stat?: number | null
          session_id?: string | null
          signature?: string | null
          speed_stat?: number | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          backstory?: string | null
          created_at?: string
          id?: string
          image_description?: string | null
          image_url?: string
          level?: number
          magic_stat?: number | null
          name?: string
          power_stat?: number | null
          session_id?: string | null
          signature?: string | null
          speed_stat?: number | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      user_likes: {
        Row: {
          card_id: string
          created_at: string
          id: string
          user_id: string
        }
        Insert: {
          card_id: string
          created_at?: string
          id?: string
          user_id: string
        }
        Update: {
          card_id?: string
          created_at?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_likes_card_id_fkey"
            columns: ["card_id"]
            isOneToOne: false
            referencedRelation: "leaderboard_cards"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          granted_at: string
          granted_by: string | null
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          granted_at?: string
          granted_by?: string | null
          id?: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          granted_at?: string
          granted_by?: string | null
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      weekly_winners: {
        Row: {
          card_creator: string | null
          card_id: string
          card_location: string | null
          card_name: string
          created_at: string
          final_likes: number
          id: string
          week_end_date: string
          week_start_date: string
        }
        Insert: {
          card_creator?: string | null
          card_id: string
          card_location?: string | null
          card_name: string
          created_at?: string
          final_likes: number
          id?: string
          week_end_date: string
          week_start_date: string
        }
        Update: {
          card_creator?: string | null
          card_id?: string
          card_location?: string | null
          card_name?: string
          created_at?: string
          final_likes?: number
          id?: string
          week_end_date?: string
          week_start_date?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_month_start: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_current_week_start: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_next_character_level: {
        Args: { character_name: string }
        Returns: number
      }
      grant_admin_role: {
        Args: { _user_id: string }
        Returns: boolean
      }
      has_role: {
        Args: {
          _role: Database["public"]["Enums"]["app_role"]
          _user_id: string
        }
        Returns: boolean
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      reset_monthly_likes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      reset_weekly_likes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      revoke_admin_role: {
        Args: { _user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      app_role: "admin" | "moderator" | "user"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "moderator", "user"],
    },
  },
} as const
