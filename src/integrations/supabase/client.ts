// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://rjgyfdwkocwklhyaqoqz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJqZ3lmZHdrb2N3a2xoeWFxb3F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxMTI5NjYsImV4cCI6MjA2ODY4ODk2Nn0.usx7TfY_p653NbwlTqTHpOLhOyzn2DLKJZXPNZ9-Syk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});