import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  containerClassName?: string;
  size?: 'thumbnail' | 'card' | 'full';
  showSkeleton?: boolean;
  fallbackSrc?: string;
}

const sizeClasses = {
  thumbnail: 'w-16 h-16',
  card: 'w-full h-full',
  full: 'w-full h-full'
};

// Optimize image URL for different sizes
const getOptimizedImageUrl = (originalUrl: string, size: string) => {
  if (!originalUrl.includes('supabase.co/storage')) return originalUrl;
  
  // Add transform parameters for Supabase images
  const url = new URL(originalUrl);
  switch (size) {
    case 'thumbnail':
      url.searchParams.set('width', '128');
      url.searchParams.set('height', '128');
      break;
    case 'card':
      url.searchParams.set('width', '400');
      url.searchParams.set('height', '400');
      break;
    default:
      break;
  }
  url.searchParams.set('quality', '80');
  return url.toString();
};

export function LazyImage({ 
  src, 
  alt, 
  className = '', 
  containerClassName = '',
  size = 'card',
  showSkeleton = true,
  fallbackSrc = '/placeholder.svg'
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false); // Fixed: start with false for proper lazy loading
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Get optimized image URL
  const optimizedSrc = getOptimizedImageUrl(src, size);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Reset loading states when src changes to ensure image updates
  useEffect(() => {
    setIsLoaded(false);
    setHasError(false);
  }, [src]);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    setIsLoaded(true);
  };

  return (
    <div 
      ref={containerRef}
      className={cn(
        'relative overflow-hidden',
        sizeClasses[size],
        containerClassName
      )}
    >
      {showSkeleton && !isLoaded && (
        <Skeleton className="absolute inset-0 z-10" />
      )}
      
      {isInView && (
        <img
          key={hasError ? `${src}-fallback` : src}
          ref={imgRef}
          src={hasError ? fallbackSrc : optimizedSrc}
          alt={alt}
          loading="lazy"
          decoding="async"
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          style={{
            filter: isLoaded ? 'none' : 'blur(8px)',
          }}
        />
      )}
      
    </div>
  );
}