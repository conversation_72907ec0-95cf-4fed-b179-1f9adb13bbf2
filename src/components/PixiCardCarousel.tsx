import { useState, useEffect, useMemo } from "react";
import { ChevronLeft, ChevronRight, Crown } from "lucide-react";
import { PlayingCardStar } from "./ui/playing-card-star";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CompactCharacterCard } from "./CompactCharacterCard";
import confetti from "canvas-confetti";
import { useLeaderboardCards, useMonthlyWinner } from '@/hooks/useImageData';

interface PixiCard {
  id: string;
  name: string;
  backstory: string;
  image_url: string;
  likes: number;
  signature: string;
  power_stat?: number;
  magic_stat?: number;
  speed_stat?: number;
}

interface PixiCardCarouselProps {
  onCreateOwn: () => void;
}

export function PixiCardCarousel({ onCreateOwn }: PixiCardCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { data: rawCards, isLoading } = useLeaderboardCards();
  const { data: monthlyWinner, isLoading: isWinnerLoading } = useMonthlyWinner();

  // Transform data to existing format
  const allPixiCards: PixiCard[] = useMemo(() => {
    if (!rawCards) return [];
    return rawCards.map(card => ({
      id: card.id,
      name: card.name,
      backstory: card.backstory || '',
      image_url: card.image_url,
      likes: card.likes,
      signature: card.signature || '',
      power_stat: card.power_stat,
      magic_stat: card.magic_stat,
      speed_stat: card.speed_stat
    }));
  }, [rawCards]);

  const lastWeekWinner: PixiCard | null = useMemo(() => {
    if (!monthlyWinner?.card_data) return null;
    const cardData = monthlyWinner.card_data;
    return {
      id: cardData.id,
      name: cardData.name,
      backstory: cardData.backstory || '',
      image_url: cardData.image_url,
      likes: cardData.likes,
      signature: cardData.signature || '',
      power_stat: cardData.power_stat,
      magic_stat: cardData.magic_stat,
      speed_stat: cardData.speed_stat
    };
  }, [monthlyWinner]);

  const loading = isLoading || isWinnerLoading;

  // Create random rotation with winner every 10th card on homepage
  const displayCards = useMemo(() => {
    if (allPixiCards.length === 0) return [];

    // Shuffle all cards for random rotation
    const shuffled = [...allPixiCards];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    // Remove winner from the pool to avoid duplicates
    const base = lastWeekWinner ? shuffled.filter(c => c.id !== lastWeekWinner.id) : shuffled;

    const rotationCards: PixiCard[] = [];
    for (let i = 0; i < base.length; i++) {
      // Insert last month's winner at every 10th position (10, 20, ...)
      if (((rotationCards.length + 1) % 10 === 0) && lastWeekWinner) {
        rotationCards.push(lastWeekWinner);
      }
      rotationCards.push(base[i]);
    }

    // Ensure at least one winner appears if we have fewer than 10 cards
    if (lastWeekWinner && rotationCards.findIndex(c => c.id === lastWeekWinner.id) === -1) {
      const insertIndex = Math.min(9, rotationCards.length);
      rotationCards.splice(insertIndex, 0, lastWeekWinner);
    }

    return rotationCards;
  }, [allPixiCards, lastWeekWinner]);

  useEffect(() => {
    if (displayCards.length > 0) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % displayCards.length);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [displayCards.length]);

  // Trigger confetti when landing on last month's winner
  useEffect(() => {
    const currentCard = displayCards[currentIndex];
    if (lastWeekWinner && currentCard && currentCard.id === lastWeekWinner.id) {
      confetti({
        particleCount: 150,
        spread: 70,
        origin: { y: 0.6 },
        colors: ['#FFD700', '#FFA500', '#FF6347', '#32CD32', '#1E90FF', '#9370DB']
      });
    }
  }, [currentIndex, displayCards, lastWeekWinner]);

  // No longer need loadPixiCards - React Query handles data loading

  const nextCard = () => {
    setCurrentIndex((prev) => (prev + 1) % displayCards.length);
  };

  const prevCard = () => {
    setCurrentIndex((prev) => (prev - 1 + displayCards.length) % displayCards.length);
  };

  if (loading) {
    return (
      <div className="relative w-full max-w-2xl mx-auto">
        <div className="bg-card-gradient rounded-3xl p-8 shadow-magical border border-border animate-pulse">
          <div className="h-8 bg-muted rounded mb-6"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (displayCards.length === 0) {
    return (
      <div className="relative w-full max-w-2xl mx-auto">
        <div className="bg-card-gradient rounded-3xl p-8 shadow-magical border border-border text-center">
          <div className="flex items-center justify-center mb-6">
            <PlayingCardStar className="w-8 h-8 text-accent mr-3 animate-sparkle" />
            <h2 className="text-2xl font-bold text-foreground">
              Featured PixiCards
            </h2>
            <PlayingCardStar className="w-8 h-8 text-accent ml-3 animate-sparkle" />
          </div>
          <p className="text-muted-foreground mb-6">Be the first to create a PixiCard!</p>
          <Button 
            onClick={onCreateOwn}
            className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-8 py-3 rounded-full"
          >
            <PlayingCardStar className="w-5 h-5 mr-2" />
            Create First PixiCard!
          </Button>
        </div>
      </div>
    );
  }

  const currentCard = displayCards[currentIndex];
  const isWinnerCard = lastWeekWinner && currentCard && currentCard.id === lastWeekWinner.id;

  // Add safety check for currentCard
  if (!currentCard) {
    return (
      <div className="relative w-full max-w-2xl mx-auto">
        <div className="bg-card-gradient rounded-3xl p-8 shadow-magical border border-border text-center">
          <div className="flex items-center justify-center mb-6">
            <PlayingCardStar className="w-8 h-8 text-accent mr-3 animate-sparkle" />
            <h2 className="text-2xl font-bold text-foreground">
              Featured PixiCards
            </h2>
            <PlayingCardStar className="w-8 h-8 text-accent ml-3 animate-sparkle" />
          </div>
          <p className="text-muted-foreground mb-6">Loading cards...</p>
          <Button 
            onClick={onCreateOwn}
            className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-8 py-3 rounded-full"
          >
            <PlayingCardStar className="w-5 h-5 mr-2" />
            Create Your PixiCard!
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full max-w-6xl mx-auto px-2">
      <div className="bg-card-gradient rounded-3xl p-3 sm:p-4 lg:p-5 shadow-magical border border-border">
        <div className="flex items-center justify-center mb-4 sm:mb-6">
          {isWinnerCard ? (
            <>
              <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-500 mr-2 sm:mr-3 animate-bounce" />
              <h2 className="text-xl sm:text-2xl font-bold text-foreground text-center">
                🏆 Last Month's Winner! 🏆
              </h2>
              <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-500 ml-2 sm:ml-3 animate-bounce" />
            </>
          ) : (
            <>
              <PlayingCardStar className="w-6 h-6 sm:w-8 sm:h-8 text-accent mr-2 sm:mr-3 animate-sparkle" />
              <h2 className="text-xl sm:text-2xl font-bold text-foreground text-center">
                Featured PixiCards
              </h2>
              <PlayingCardStar className="w-6 h-6 sm:w-8 sm:h-8 text-accent ml-2 sm:ml-3 animate-sparkle" />
            </>
          )}
        </div>
        
        <div className="relative flex items-center justify-center min-h-[400px] sm:min-h-[300px]">
          <Button
            variant="outline"
            size="icon"
            onClick={prevCard}
            className="absolute left-2 sm:left-4 z-10 bg-background/90 hover:bg-background transition-bounce w-12 h-12 sm:w-10 sm:h-10"
          >
            <ChevronLeft className="w-6 h-6 sm:w-5 sm:h-5" />
          </Button>
          
          <div className="mx-16 sm:mx-20">
            <CompactCharacterCard
              key={currentCard.id}
              imageUrl={currentCard.image_url || ''}
              name={currentCard.name || 'Unknown Character'}
              description={currentCard.backstory || 'No description available'}
              creator={currentCard.signature || 'Anonymous'}
              likes={currentCard.likes || 0}
              powerStat={currentCard.power_stat}
              magicStat={currentCard.magic_stat}
              speedStat={currentCard.speed_stat}
            />
            
            {/* Winner celebration text */}
            {isWinnerCard && (
              <div className="mt-4 text-center bg-gradient-to-r from-yellow-100 via-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:via-yellow-800/10 dark:to-yellow-900/20 border-2 border-yellow-300 dark:border-yellow-600 rounded-xl p-4 shadow-lg">
                <p className="text-sm sm:text-base font-bold text-yellow-800 dark:text-yellow-200 mb-1">
                  🎉 Congratulations! 🎉
                </p>
                <p className="text-xs sm:text-sm text-yellow-700 dark:text-yellow-300">
                  <span className="font-semibold">{currentCard.signature || "This amazing creator"}</span> won the most stars last month with <span className="font-bold">{currentCard.likes} ⭐</span> and earned a special prize!
                </p>
              </div>
            )}
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={nextCard}
            className="absolute right-2 sm:right-4 z-10 bg-background/90 hover:bg-background transition-bounce w-12 h-12 sm:w-10 sm:h-10"
          >
            <ChevronRight className="w-6 h-6 sm:w-5 sm:h-5" />
          </Button>
        </div>
        
        <div className="flex justify-center mt-4 sm:mt-6">
          <Button 
            onClick={onCreateOwn}
            className="bg-hero-gradient hover:scale-105 transition-bounce shadow-magical text-white font-bold px-6 sm:px-8 py-3 rounded-full text-sm sm:text-base min-h-[48px]"
          >
            <PlayingCardStar className="w-5 h-5 mr-2" />
            Start Creating!
          </Button>
        </div>
        
        <div className="flex justify-center mt-4 space-x-2">
          {displayCards.map((card, index) => (
            <button
              key={`${card.id}-${index}`}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 sm:w-2 sm:h-2 rounded-full transition-all min-h-[32px] min-w-[32px] sm:min-h-0 sm:min-w-0 ${
                index === currentIndex 
                  ? 'bg-primary scale-125' 
                  : 'bg-muted hover:bg-muted-foreground/50'
              } ${lastWeekWinner && card.id === lastWeekWinner.id ? 'border-2 border-yellow-500' : ''}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}