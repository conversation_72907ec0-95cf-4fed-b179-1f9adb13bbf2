import { useState, useEffect, useMemo } from "react";
import { Wand2, Palette, Sparkles } from "lucide-react";
import { useAllCardsForDisplay } from '@/hooks/useImageData';

const loadingMessages = [
  "✨ Sprinkling pixie dust...",
  "🎨 Mixing magical colors...",
  "🌟 Weaving enchanted pixels...",
  "🎭 Creating character magic...",
  "🔮 Channeling creative energy...",
  "🦄 Adding unicorn sparkles..."
];

type LeaderboardCard = {
  id: string;
  name: string;
  image_url: string;
};

interface SimpleLoadingScreenProps {
  isVisible: boolean;
}

export function SimpleLoadingScreen({ isVisible }: SimpleLoadingScreenProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { data: rawAllCards } = useAllCardsForDisplay();

  // Transform data to existing format
  const allCards: LeaderboardCard[] = useMemo(() => {
    if (!rawAllCards) return [];
    return rawAllCards.map(card => ({
      id: card.id,
      name: card.name,
      image_url: card.image_url
    }));
  }, [rawAllCards]);


  const displayCards = useMemo(() => {
    if (allCards.length === 0) return [];
    const shuffled = [...allCards];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, [allCards]);

  useEffect(() => {
    if (isVisible) {
      const messageInterval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % loadingMessages.length);
      }, 2000);

      const imageInterval = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % Math.max(displayCards.length, 1));
      }, 4000);
      
      return () => {
        clearInterval(messageInterval);
        clearInterval(imageInterval);
      };
    }
  }, [isVisible, displayCards.length]);

  // No longer need separate useEffect - displayCards is computed via useMemo above

  if (!isVisible) return null;

  const currentImage = displayCards[currentImageIndex];

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card-gradient rounded-3xl p-4 sm:p-8 shadow-magical border border-border w-full max-w-md sm:max-w-lg text-center">
        <div className="flex items-center justify-center mb-4 sm:mb-6">
          <Wand2 className="w-6 h-6 sm:w-8 sm:h-8 text-accent mr-2 sm:mr-3 animate-spin" />
          <h2 className="text-lg sm:text-2xl font-bold text-foreground">
            Creating Magic...
          </h2>
          <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-primary ml-2 sm:ml-3 animate-pulse" />
        </div>
        
        {/* Rotating Leaderboard Images */}
        {displayCards.length > 0 && currentImage && (
          <div className="mb-6 relative">
            <div className="relative w-48 h-48 sm:w-56 sm:h-56 mx-auto mb-3 rounded-xl overflow-hidden border border-primary/20 shadow-md">
              <img 
                src={currentImage.image_url} 
                alt={currentImage.name}
                className="w-full h-full object-cover transition-opacity duration-500"
                onError={(e) => {
                  // Fallback if image fails to load
                  e.currentTarget.style.display = 'none';
                }}
              />
              {/* Magical overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-primary/10 via-transparent to-accent/5 pointer-events-none"></div>
              {/* Sparkle effect */}
              <div className="absolute top-1 right-1">
                <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full animate-ping opacity-75"></div>
              </div>
            </div>
            <p className="text-xs text-muted-foreground font-medium">
              Featuring: {currentImage.name}
            </p>
          </div>
        )}
        
        <div className="mb-4 sm:mb-6">
          <p className="text-sm sm:text-lg text-accent font-medium animate-pulse">
            {loadingMessages[currentMessageIndex]}
          </p>
        </div>
        
        <div className="mb-4 sm:mb-6">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          </div>
        </div>
        
        <div className="flex justify-center space-x-1">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-2 h-2 rounded-full bg-primary animate-bounce`}
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}