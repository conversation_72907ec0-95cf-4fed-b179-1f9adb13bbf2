import { useState, useEffect } from "react";
import { Card } from "./ui/card";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Loader2, ArrowLeft, Search, Plus, Minus } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { formatPrice } from "@/lib/pricing";

interface PackOrderFormProps {
  onOrderSubmitted: () => void;
  onBack: () => void;
}

interface CustomerData {
  name: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone: string;
  specialInstructions: string;
}

export function PackOrderForm({ onOrderSubmitted, onBack }: PackOrderFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<CustomerData>({
    name: "",
    email: "",
    address: "",
    city: "",
    state: "",
    zipCode: "",
    country: "United Kingdom",
    phone: "",
    specialInstructions: "",
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [cards, setCards] = useState<{ id: string; name: string; image_url: string; user_id?: string; signature?: string; creator?: string }[]>([]);
  const [filteredCards, setFilteredCards] = useState<typeof cards>([]);
  const [selectedCard, setSelectedCard] = useState<{ id: string; name: string; imageUrl: string; creator?: string } | null>(null);
  const [isCardsLoading, setIsCardsLoading] = useState(true);
  const [packQuantity, setPackQuantity] = useState(1);

  // Load cards for guaranteed selection
  useEffect(() => {
    const fetchCards = async () => {
      try {
        setIsCardsLoading(true);
        const { data: leaderboardCards, error: leaderboardError } = await supabase
          .from('leaderboard_cards')
          .select('id, name, image_url, user_id, signature')
          .order('likes', { ascending: false });

        const { data: userCards, error: userError } = await supabase
          .from('user_cards')
          .select('id, name, image_url, user_id, signature')
          .order('created_at', { ascending: false });

        if (leaderboardError) throw leaderboardError;
        if (userError) throw userError;

        const isAutoGeneratedSignature = (signature?: string) => {
          if (!signature) return true;
          return /^Created \d{2}\/\d{2}\/\d{4} at \d{2}:\d{2}$/.test(signature);
        };
        const getCreatorName = (signature?: string) => {
          if (!signature || isAutoGeneratedSignature(signature)) return 'Unknown Creator';
          return signature;
        };

        const allCards = [
          ...(leaderboardCards?.filter(c => c.name && c.name.trim() !== '' && c.image_url).map(c => ({ ...c, creator: getCreatorName(c.signature) })) || []),
          ...(userCards?.filter(c => c.name && c.name.trim() !== '' && c.image_url).map(c => ({ ...c, creator: getCreatorName(c.signature) })) || []),
        ];
        allCards.sort((a, b) => {
          const aReal = !isAutoGeneratedSignature(a.signature);
          const bReal = !isAutoGeneratedSignature(b.signature);
          if (aReal && !bReal) return -1;
          if (!aReal && bReal) return 1;
          return 0;
        });

        setCards(allCards);
        setFilteredCards(allCards);
      } catch (err) {
        console.error('Error fetching cards for pack selection:', err);
        toast.error('Failed to load cards for selection.');
      } finally {
        setIsCardsLoading(false);
      }
    };
    fetchCards();
  }, []);

  // Filter search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCards(cards);
    } else {
      const q = searchQuery.toLowerCase();
      setFilteredCards(
        cards.filter(c => c.name.toLowerCase().includes(q) || (c.creator && c.creator.toLowerCase().includes(q)))
      );
    }
  }, [searchQuery, cards]);

  const totalPrice = packQuantity * 10;

  const handleInputChange = (field: keyof CustomerData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        const redirectPath = '/order?step=pack-checkout';
        try { localStorage.setItem('postAuthRedirect', redirectPath); } catch {}
        window.location.href = `/auth?redirect=${encodeURIComponent(redirectPath)}`;
        return;
      }

      const { data, error } = await supabase.functions.invoke('create-payment', {
        body: {
          currency: 'gbp',
          items: [{ name: 'PixiCards Pack', amount: 10, quantity: packQuantity }],
          orderContext: { type: 'pack', guaranteed_card: selectedCard ? selectedCard : undefined }
        }
      });
      if (error || !data?.url) throw new Error(error?.message || 'Failed to start checkout');
      window.open(data.url, '_blank');

      toast.success('Redirecting to Stripe Checkout...');
    } catch (error) {
      console.error('Error starting pack checkout:', error);
      toast.error('Failed to start checkout. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="mb-6">
            <Button
              variant="ghost"
              onClick={onBack}
              className="mb-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Selection
            </Button>
            <h1 className="text-3xl font-bold mb-2">Pack Order Checkout</h1>
            <p className="text-muted-foreground">Complete your PixiCards Premium Pack order</p>
          </div>

          {/* Pack Visual */}
          <Card className="p-6 mb-6 bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20">
            <div className="text-center">
              <div className="relative mb-4">
                {/* Stacked Cards Visual */}
                <div className="relative w-32 h-44 mx-auto">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary to-accent rounded-lg shadow-lg transform rotate-3"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-accent to-primary rounded-lg shadow-lg transform -rotate-2 translate-x-1"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-primary to-accent rounded-lg shadow-xl transform rotate-1 translate-x-2"></div>
                  <div className="absolute top-2 left-2 right-2 text-white text-xs font-bold">PixiCards</div>
                </div>
                <div className="absolute -top-2 -right-2 bg-accent text-accent-foreground px-3 py-1 rounded-full text-sm font-bold">
                  £10
                </div>
              </div>
              <h3 className="text-xl font-bold mb-2">PixiCards Premium Pack</h3>
              <p className="text-sm text-muted-foreground">
                Curated selection of premium cards from our community
              </p>
            </div>
          </Card>

          {/* Guaranteed Card Picker (optional) */}
          <Card className="p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">Guaranteed Card (optional)</h2>
              {selectedCard && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setSelectedCard(null)}
                >
                  Clear selection
                </Button>
              )}
            </div>

            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Search className="w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search by card name or creator..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 max-h-64 overflow-y-auto border rounded-md p-3">
                {isCardsLoading ? (
                  <div className="col-span-full text-center text-muted-foreground">Loading cards...</div>
                ) : filteredCards.length === 0 ? (
                  <div className="col-span-full text-center text-muted-foreground">No matching cards</div>
                ) : (
                  filteredCards.slice(0, 30).map((c) => (
                    <button
                      key={c.id}
                      type="button"
                      onClick={() => setSelectedCard({ id: c.id, name: c.name, imageUrl: c.image_url, creator: c.creator })}
                      className={`group relative rounded-md border overflow-hidden text-left focus:outline-none focus:ring-2 focus:ring-accent transition ${
                        selectedCard?.id === c.id ? 'ring-2 ring-accent border-accent' : 'hover:border-accent/50'
                      }`}
                    >
                      <img src={c.image_url} alt={c.name} className="w-full h-28 object-cover" />
                      <div className="p-2">
                        <div className="text-sm font-medium truncate">{c.name}</div>
                        <div className="text-xs text-muted-foreground truncate">{c.creator || 'Unknown'}</div>
                      </div>
                    </button>
                  ))
                )}
              </div>

              {selectedCard && (
                <div className="flex items-center gap-3 p-3 border rounded-md bg-accent/5">
                  <img src={selectedCard.imageUrl} alt={selectedCard.name} className="w-12 h-12 object-cover rounded" />
                  <div className="min-w-0">
                    <div className="font-medium truncate">Guaranteed: {selectedCard.name}</div>
                    {selectedCard.creator && (
                      <div className="text-sm text-muted-foreground truncate">by {selectedCard.creator}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Checkout CTA (Shipping collected at Stripe) */}
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-2">Secure Checkout</h2>
            <p className="text-sm text-muted-foreground mb-4">
              Shipping address and recipient details will be collected on Stripe Checkout.
            </p>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Quantity selector */}
              <div className="flex items-center justify-between">
                <Label className="text-base">Quantity</Label>
                <div className="flex items-center gap-2">
                  <Button type="button" variant="outline" onClick={() => setPackQuantity((q) => Math.max(1, q - 1))} aria-label="Decrease quantity">
                    <Minus className="w-4 h-4" />
                  </Button>
                  <div className="w-12 text-center font-semibold" aria-live="polite">{packQuantity}</div>
                  <Button type="button" variant="outline" onClick={() => setPackQuantity((q) => Math.min(20, q + 1))} aria-label="Increase quantity">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="pt-2 border-t">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-lg font-semibold">Total:</span>
                  <span className="text-2xl font-bold text-accent">{formatPrice(totalPrice)}</span>
                </div>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-accent hover:bg-accent/90 text-accent-foreground"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Redirecting to Checkout...
                    </>
                  ) : (
                    'Go to Secure Checkout'
                  )}
                </Button>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
}