import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const UpdateStories = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      console.log('Calling update-long-descriptors function...');
      const { data, error } = await supabase.functions.invoke('update-long-descriptors', {
        body: {}
      });

      if (error) {
        console.error('Error:', error);
        toast({
          title: "Update Failed",
          description: error.message,
          variant: "destructive"
        });
      } else {
        console.log('Update response:', data);
        toast({
          title: "Stories Updated!",
          description: `Updated ${data.updated} cards successfully`,
        });
      }
    } catch (error) {
      console.error('Update error:', error);
      toast({
        title: "Update Failed",
        description: "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="p-4">
      <Button 
        onClick={handleUpdate} 
        disabled={isUpdating}
        variant="secondary"
      >
        {isUpdating ? 'Updating Stories...' : 'Update All Card Stories'}
      </Button>
    </div>
  );
};

export default UpdateStories;