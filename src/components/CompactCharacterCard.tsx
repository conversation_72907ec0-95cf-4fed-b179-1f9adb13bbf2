
interface CompactCharacterCardProps {
  imageUrl: string;
  name: string;
  description: string;
  creator: string;
  likes: number;
  powerStat?: number;
  magicStat?: number;
  speedStat?: number;
}

export function CompactCharacterCard({ 
  imageUrl, 
  name, 
  description, 
  creator, 
  likes,
  powerStat,
  magicStat,
  speedStat
}: CompactCharacterCardProps) {
  return (
    <div className="w-full max-w-sm mx-auto">
      {/* Trading Card with Magical Border - Portrait Format */}
      <div className="relative bg-gradient-to-br from-pink-400 via-purple-500 to-cyan-400 p-1 rounded-2xl shadow-2xl transform hover:rotate-1 hover:scale-105 transition-all duration-300 aspect-[5/4] overflow-visible">
        {/* Sparkly border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-yellow-300 via-pink-300 to-purple-300 rounded-2xl opacity-75 blur-sm animate-pulse"></div>
        
        {/* Inner card with fun kid-friendly styling */}
        <div className="relative bg-gradient-to-br from-yellow-100 via-pink-50 to-blue-100 rounded-xl overflow-hidden border-2 border-white shadow-inner">
          
          {/* Card Header - Taller, clean text only */}
          <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 px-4 py-4 relative overflow-hidden">
            {/* Dots removed to match leaderboard style */}
            <div className="relative flex justify-center items-center">
              <span className="text-lg sm:text-xl font-black text-white drop-shadow-lg tracking-wider">PIXICARDS</span>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-2">
            {/* Character Name Header */}
            <div className="text-center mb-3 bg-gradient-to-r from-yellow-200 via-pink-200 to-purple-200 rounded-lg p-2 border-2 border-yellow-300 shadow-lg">
              <h3 className="text-base font-card font-black text-purple-800 tracking-wide drop-shadow-sm">
                {name || "🌟 Magical Friend 🌟"}
              </h3>
            </div>

            {/* Character Image */}
            <div className="relative mb-3">
              <div className="aspect-[3/4] rounded-xl overflow-hidden bg-gradient-to-br from-pink-300 to-blue-300 p-1 border-2 border-white shadow-xl">
                <img 
                  src={imageUrl} 
                  alt={name || "Generated character"}
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
            </div>

            {/* Stats Display */}
            {(powerStat || magicStat || speedStat) && (
              <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg">
                <div className="flex justify-center items-center divide-x divide-gray-300">
                  {powerStat && (
                    <div className="flex flex-col items-center px-4">
                      <div className="flex items-center gap-1 mb-1">
                        <span className="text-red-500">⭐</span>
                        <span className="text-xs font-medium text-red-600">Power</span>
                      </div>
                      <span className="text-lg font-bold text-gray-800">{powerStat}</span>
                    </div>
                  )}
                  {magicStat && (
                    <div className="flex flex-col items-center px-4">
                      <div className="flex items-center gap-1 mb-1">
                        <span className="text-purple-500">🔮</span>
                        <span className="text-xs font-medium text-purple-600">Magic</span>
                      </div>
                      <span className="text-lg font-bold text-gray-800">{magicStat}</span>
                    </div>
                  )}
                  {speedStat && (
                    <div className="flex flex-col items-center px-4">
                      <div className="flex items-center gap-1 mb-1">
                        <span className="text-blue-500">💨</span>
                        <span className="text-xs font-medium text-blue-600">Speed</span>
                      </div>
                      <span className="text-lg font-bold text-gray-800">{speedStat}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Character Story Box */}
            <div className="bg-gradient-to-br from-white to-blue-50 border-2 border-blue-200 rounded-lg p-2 mb-2 shadow-lg">
              <p className="text-xs font-card text-purple-800 text-center leading-relaxed bg-white rounded-lg p-2 border border-purple-100 line-clamp-3">
                {description || "This magical friend loves adventures and spreading joy! ✨🌈"}
              </p>
            </div>

            {/* Creator Footer */}
            <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 text-white rounded-lg p-2 text-center shadow-lg">
              <div className="text-xs font-card font-bold text-white drop-shadow">
                {creator ? `Created by ${creator}` : "✨ Magical Character ✨"}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}