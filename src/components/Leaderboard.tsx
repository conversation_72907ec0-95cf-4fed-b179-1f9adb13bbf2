import { useState, useMemo, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { CharacterCard, CardData } from "./CharacterCard";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "./ui/tabs";
import { Trophy, Star, Crown, Medal, Wand2, Settings, Calendar, Sparkles, Heart, Search, RefreshCw } from "lucide-react";
import { PlayingCardStar } from "./ui/playing-card-star";
import { supabase } from "@/integrations/supabase/client";
import { Link, useNavigate } from "react-router-dom";
import confetti from 'canvas-confetti';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { useLeaderboardCards, useMonthlyWinner, type LeaderboardCard as ImageDataCard, type MonthlyWinner as ImageDataWinner } from '@/hooks/useImageData';
import { Popover, PopoverTrigger, PopoverContent } from './ui/popover';
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from './ui/command';
type LeaderboardCard = CardData & { 
  id: string; 
  userId?: string;
  likes: number; 
  monthlyLikes: number;
  level: number; 
  createdAt: Date;
  monthStartDate: Date;
  power_stat?: number;
  magic_stat?: number;
  speed_stat?: number;
};

type MonthlyWinner = {
  id: string;
  card_id: string;
  month_start_date: string;
  month_end_date: string;
  final_likes: number;
  card_name: string;
  card_creator: string;
  card_location: string;
  card_data?: LeaderboardCard;
};

export function Leaderboard({ mode = 'default' }: { mode?: 'default' | 'collection' }) {
  const { data: rawCards, isLoading, refetch: refetchCards } = useLeaderboardCards();
  const { data: rawMonthlyWinner, isLoading: isWinnerLoading, refetch: refetchWinner } = useMonthlyWinner();
  const [activeTab, setActiveTab] = useState('month');
  const [showWinnerAnimation, setShowWinnerAnimation] = useState(false);
  const [celebrationTriggered, setCelebrationTriggered] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [addSearchQuery, setAddSearchQuery] = useState("");
  const [isAddOpen, setIsAddOpen] = useState(false);

  // Transform data to maintain existing format
  const cards: LeaderboardCard[] = useMemo(() => {
    if (!rawCards) return [];
  return rawCards.map(card => ({
    id: card.id,
    userId: (card as any).user_id,
    name: card.name,
    backstory: card.backstory || '',
    signature: card.signature || '',
    imageUrl: card.image_url,
    likes: card.likes,
    monthlyLikes: card.monthly_likes,
    level: card.level,
    power_stat: card.power_stat,
    magic_stat: card.magic_stat,
    speed_stat: card.speed_stat,
    createdAt: new Date(card.created_at),
    monthStartDate: new Date(card.month_start_date)
  }));
  }, [rawCards]);

  const monthlyWinner: MonthlyWinner | null = useMemo(() => {
    if (!rawMonthlyWinner) return null;
    const cardData = rawMonthlyWinner.card_data;
    return {
      ...rawMonthlyWinner,
      card_creator: rawMonthlyWinner.card_creator || 'Unknown',
      card_location: rawMonthlyWinner.card_location || 'Unknown',
      card_data: cardData ? {
        id: cardData.id,
        name: cardData.name,
        backstory: cardData.backstory || '',
        signature: cardData.signature || '',
        imageUrl: cardData.image_url,
        likes: cardData.likes,
        monthlyLikes: cardData.monthly_likes,
        level: cardData.level,
        power_stat: cardData.power_stat,
        magic_stat: cardData.magic_stat,
        speed_stat: cardData.speed_stat,
        createdAt: new Date(cardData.created_at),
        monthStartDate: new Date(cardData.month_start_date)
      } : undefined
    };
  }, [rawMonthlyWinner]);

  const loading = isLoading || isWinnerLoading;

  const { user, signOut } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [likedIds, setLikedIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    const loadLikes = async () => {
      if (!user) {
        setLikedIds(new Set());
        return;
      }
      const { data, error } = await supabase
        .from('user_likes')
        .select('card_id')
        .eq('user_id', user.id);
      if (!error) {
        setLikedIds(new Set((data || []).map((d: any) => d.card_id)));
      }
    };
    loadLikes();
  }, [user]);

  const handleLike = async (cardId: string) => {
    if (!user) {
      toast({ title: 'Sign in required', description: 'Please sign in to star cards.' });
      navigate('/auth');
      return;
    }

    const currentCard = cards.find(c => c.id === cardId);
    if (!currentCard) return;

    const isLiked = likedIds.has(cardId);

    try {
      if (isLiked) {
        await supabase.from('user_likes').delete().eq('user_id', user.id).eq('card_id', cardId);
        await supabase.from('leaderboard_cards').update({
          likes: Math.max(0, currentCard.likes - 1),
          monthly_likes: Math.max(0, currentCard.monthlyLikes - 1)
        }).eq('id', cardId);
        const next = new Set(likedIds);
        next.delete(cardId);
        setLikedIds(next);
      } else {
        await supabase.from('user_likes').insert({ user_id: user.id, card_id: cardId });
        await supabase.from('leaderboard_cards').update({
          likes: currentCard.likes + 1,
          monthly_likes: currentCard.monthlyLikes + 1
        }).eq('id', cardId);
        const next = new Set(likedIds);
        next.add(cardId);
        setLikedIds(next);
      }
      await Promise.all([refetchCards(), refetchWinner()]);
    } catch (error) {
      console.error('Error handling like:', error);
      toast({ title: 'Action failed', description: 'Could not update your star. Please try again.', variant: 'destructive' });
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([refetchCards(), refetchWinner()]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getWeekDateRange = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay()); // Sunday
    startOfWeek.setHours(0, 0, 0, 0);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // Saturday
    endOfWeek.setHours(23, 59, 59, 999);
    
    return {
      start: startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      end: endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
    };
  };

  const getLastWeekDateRange = (weekStartDate: string) => {
    const start = new Date(weekStartDate);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    
    return {
      start: start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      end: end.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
    };
  };

  const getRankIcon = (position: number) => {
    switch (position) {
      case 0: return <Crown className="w-6 h-6 text-yellow-500" />;
      case 1: return <Medal className="w-6 h-6 text-gray-400" />;
      case 2: return <Trophy className="w-6 h-6 text-amber-600" />;
      default: return <Star className="w-5 h-5 text-accent" />;
    }
  };

  const getRankLabel = (position: number) => {
    switch (position) {
      case 0: return "Star of the Month";
      case 1: return "Runner-up";
      case 2: return "Third Place";
      default: return `#${position + 1}`;
    }
  };

  const triggerCelebration = () => {
    // Multiple confetti bursts for dramatic effect
    const celebration = () => {
      confetti({
        particleCount: 150,
        spread: 70,
        origin: { y: 0.6 },
        colors: ['#FFD700', '#FF69B4', '#9370DB', '#00CED1', '#32CD32']
      });
      
      setTimeout(() => {
        confetti({
          particleCount: 100,
          spread: 100,
          origin: { y: 0.4 },
          colors: ['#FFD700', '#FF1493', '#8A2BE2']
        });
      }, 300);
      
      setTimeout(() => {
        confetti({
          particleCount: 80,
          spread: 50,
          origin: { y: 0.7 },
          colors: ['#FF4500', '#FF69B4', '#32CD32']
        });
      }, 600);
    };
    
    celebration();
  };

  // Auto-trigger celebration when winner tab is selected
  useEffect(() => {
    if (activeTab === 'winner' && monthlyWinner && !celebrationTriggered) {
      setShowWinnerAnimation(true);
      setCelebrationTriggered(true);
      
      // Trigger confetti after card animation completes
      setTimeout(() => {
        triggerCelebration();
      }, 2000);
      
      // Additional celebration bursts
      setTimeout(() => {
        confetti({
          particleCount: 50,
          spread: 60,
          origin: { y: 0.8 },
          colors: ['#FFD700', '#FF69B4']
        });
      }, 4000);
    }
    
    // Reset animation when switching away from winner tab
    if (activeTab !== 'winner') {
      setShowWinnerAnimation(false);
      setCelebrationTriggered(false);
    }
  }, [activeTab, monthlyWinner, celebrationTriggered]);

  // Sort cards by month or all time
  const getMonthSortedCards = () => {
    return [...cards].sort((a, b) => {
      const likesComparison = b.monthlyLikes - a.monthlyLikes;
      return likesComparison !== 0 ? likesComparison : b.createdAt.getTime() - a.createdAt.getTime();
    });
  };

  const getAllTimeSortedCards = () => {
    return [...cards].sort((a, b) => {
      const likesComparison = b.likes - a.likes;
      return likesComparison !== 0 ? likesComparison : b.createdAt.getTime() - a.createdAt.getTime();
    });
  };

  // Filter cards based on search query
  const filterCards = (cardsToFilter: LeaderboardCard[]) => {
    if (!searchQuery.trim()) return cardsToFilter;
    
    const query = searchQuery.toLowerCase();
    return cardsToFilter.filter(card => 
      card.name.toLowerCase().includes(query) ||
      card.backstory.toLowerCase().includes(query) ||
      card.signature.toLowerCase().includes(query)
    );
  };

// Filtered sorted cards using useMemo for performance
const filteredMonthSortedCards = useMemo(() => filterCards(getMonthSortedCards()), [cards, searchQuery]);
const filteredAllTimeSortedCards = useMemo(() => filterCards(getAllTimeSortedCards()), [cards, searchQuery]);
const filteredCollectionCards = useMemo(() => {
  const liked = cards.filter(c => likedIds.has(c.id));
  const owned = user ? cards.filter(c => c.userId === user.id) : [];
  const combinedMap = new Map<string, LeaderboardCard>();
  [...liked, ...owned].forEach(c => combinedMap.set(c.id, c));
  const combined = Array.from(combinedMap.values());
  return filterCards(combined.sort((a, b) => b.monthlyLikes - a.monthlyLikes));
}, [cards, likedIds, user, searchQuery]);

// Search across ALL cards to add to collection
const filteredAddCards = useMemo(() => {
  const q = addSearchQuery.trim().toLowerCase();
  if (!q) return [] as LeaderboardCard[];
  const inCollection = new Set<string>();
  likedIds.forEach(id => inCollection.add(id));
  if (user) {
    cards.filter(c => c.userId === user.id).forEach(c => inCollection.add(c.id));
  }
  return cards
    .filter(c => !inCollection.has(c.id))
    .filter(c => (
      c.name.toLowerCase().includes(q) ||
      c.backstory.toLowerCase().includes(q) ||
      c.signature.toLowerCase().includes(q)
    ))
    .slice(0, 12);
}, [addSearchQuery, cards, likedIds, user]);

// All cards available to add (exclude those already in collection)
const availableToAdd = useMemo(() => {
  const inCollection = new Set<string>();
  likedIds.forEach(id => inCollection.add(id));
  if (user) {
    cards.filter(c => c.userId === user.id).forEach(c => inCollection.add(c.id));
  }
  return cards.filter(c => !inCollection.has(c.id));
}, [cards, likedIds, user]);

const availableToAddFiltered = useMemo(() => {
  const q = addSearchQuery.trim().toLowerCase();
  if (!q) return availableToAdd;
  return availableToAdd.filter(c => (
    c.name.toLowerCase().includes(q) ||
    c.backstory.toLowerCase().includes(q) ||
    c.signature.toLowerCase().includes(q)
  ));
}, [availableToAdd, addSearchQuery]);

  const renderLeaderboardGrid = (sortedCards: LeaderboardCard[], isWeekly: boolean, showLikes: boolean = true) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {sortedCards.map((card, index) => (
        <div key={card.id} className="relative">
          {/* Rank Badge - made more compact */}
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
            <div className="bg-card-gradient rounded-full px-3 py-1 shadow-magical border border-border flex items-center gap-1">
              {getRankIcon(index)}
              <span className="font-bold text-xs text-foreground">
                {getRankLabel(index)}
              </span>
            </div>
          </div>

          {/* Special effects for top 3 */}
          <div className={`relative ${
            index === 0 ? 'animate-glow' : 
            index === 1 ? 'animate-float' : 
            index === 2 ? 'animate-bounce-gentle' : ''
          }`}>
            <CharacterCard
              imageUrl={card.imageUrl}
              {...(showLikes ? { onLike: () => handleLike(card.id), isLiked: likedIds.has(card.id), likes: (isWeekly ? card.monthlyLikes : card.likes) } : {})}
              level={card.level}
              initialData={card}
              size="wide"
            />
          </div>

          {/* Special crown for #1 */}
          {index === 0 && (
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
              <PlayingCardStar className="w-8 h-8 text-yellow-500 animate-sparkle" />
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const renderWinnerCelebration = () => {
    if (!monthlyWinner || !monthlyWinner.card_data) {
      return (
        <div className="text-center py-12">
          <Trophy className="w-24 h-24 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-foreground mb-2">
            No monthly winner yet! 🏆
          </h3>
          <p className="text-muted-foreground">
            Check back next month to see who wins the crown! ✨
          </p>
        </div>
      );
    }

    const lastMonthStart = monthlyWinner.month_start_date;

    return (
      <div className="space-y-8 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Floating magical particles */}
          <div className="absolute top-10 left-1/4 w-2 h-2 bg-yellow-400 rounded-full animate-bounce opacity-70" style={{ animationDelay: '0s', animationDuration: '3s' }}></div>
          <div className="absolute top-20 right-1/3 w-1 h-1 bg-pink-500 rounded-full animate-bounce opacity-60" style={{ animationDelay: '1s', animationDuration: '2.5s' }}></div>
          <div className="absolute top-32 left-1/6 w-1.5 h-1.5 bg-purple-500 rounded-full animate-bounce opacity-80" style={{ animationDelay: '2s', animationDuration: '4s' }}></div>
          <div className="absolute top-16 right-1/4 w-2 h-2 bg-blue-400 rounded-full animate-bounce opacity-50" style={{ animationDelay: '0.5s', animationDuration: '3.5s' }}></div>
          <div className="absolute top-40 left-1/2 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70" style={{ animationDelay: '1.5s', animationDuration: '2s' }}></div>
        </div>

        {/* Celebration Header with dramatic entrance */}
        <div className={`text-center space-y-6 ${showWinnerAnimation ? 'animate-[bounce-in-top_1.5s_ease-out]' : 'animate-fade-in'}`}>
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/20 via-pink-500/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
            <h2 className="relative text-7xl md:text-8xl font-black bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 bg-clip-text text-transparent animate-[pulse_2s_ease-in-out_infinite] drop-shadow-2xl">
              🌟 STAR OF THE MONTH! 🌟
            </h2>
            
            {/* Floating decorative elements */}
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 animate-[float_3s_ease-in-out_infinite]">
              <Sparkles className="w-12 h-12 text-yellow-500" />
            </div>
            <div className="absolute -top-4 right-1/4 transform translate-x-1/2 animate-[float_2.5s_ease-in-out_infinite]" style={{ animationDelay: '0.5s' }}>
              <Star className="w-8 h-8 text-purple-500" />
            </div>
            <div className="absolute -top-2 left-1/4 transform -translate-x-1/2 animate-[float_3.5s_ease-in-out_infinite]" style={{ animationDelay: '1s' }}>
              <Crown className="w-10 h-10 text-yellow-400" />
            </div>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-3xl md:text-4xl font-bold text-foreground animate-[slide-up_1s_ease-out_0.5s_both]">
              🏆 Last Month's Winner! 🏆
            </h3>
          </div>
        </div>

        {/* Winner Card Display with dramatic entrance */}
        <div className="flex justify-center">
          <div className={`relative max-w-md ${showWinnerAnimation ? 'animate-[swirl-in_2s_ease-out_1s_both]' : 'animate-scale-in'} hover:scale-110 transition-all duration-500 cursor-pointer group`}
               onClick={triggerCelebration}>
            
            {/* Enhanced floating sparkles with more variety */}
            <div className="absolute -top-8 -left-8 w-6 h-6 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full animate-[bounce_2s_infinite] shadow-lg" style={{ animationDelay: '0s' }}></div>
            <div className="absolute -top-6 -right-10 w-4 h-4 bg-gradient-to-r from-pink-500 to-red-500 rounded-full animate-[bounce_1.8s_infinite] shadow-lg" style={{ animationDelay: '0.5s' }}></div>
            <div className="absolute -bottom-6 -left-10 w-5 h-5 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full animate-[bounce_2.2s_infinite] shadow-lg" style={{ animationDelay: '1s' }}></div>
            <div className="absolute -bottom-8 -right-8 w-7 h-7 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-[bounce_1.6s_infinite] shadow-lg" style={{ animationDelay: '1.5s' }}></div>
            <div className="absolute top-1/2 -left-12 w-3 h-3 bg-gradient-to-r from-green-400 to-lime-500 rounded-full animate-[bounce_2.5s_infinite] shadow-lg" style={{ animationDelay: '2s' }}></div>
            <div className="absolute top-1/2 -right-12 w-4 h-4 bg-gradient-to-r from-orange-400 to-red-500 rounded-full animate-[bounce_1.9s_infinite] shadow-lg" style={{ animationDelay: '0.8s' }}></div>
            
            {/* Multi-layered glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/30 via-pink-500/30 to-purple-600/30 rounded-3xl blur-2xl animate-pulse group-hover:blur-3xl transition-all duration-500"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-300/20 via-pink-400/20 to-purple-500/20 rounded-3xl blur-xl animate-pulse group-hover:scale-110 transition-all duration-500" style={{ animationDelay: '0.5s' }}></div>
            
            {/* Spotlight effect */}
            <div className="absolute inset-0 bg-gradient-radial from-yellow-200/20 via-transparent to-transparent rounded-3xl animate-pulse"></div>
            
            {/* The actual card with enhanced effects */}
            <div className="relative transform group-hover:rotate-1 transition-all duration-500">
              <CharacterCard
                imageUrl={monthlyWinner.card_data.imageUrl}
                onLike={() => {}} // No likes on winner display
                likes={monthlyWinner.final_likes}
                level={monthlyWinner.card_data.level}
                initialData={monthlyWinner.card_data}
              />
            </div>
          </div>
        </div>

        {/* Winner Details with staggered animations */}
        <div className="text-center space-y-6">
          <div className="bg-gradient-to-br from-card via-card/95 to-card/90 rounded-3xl p-8 shadow-2xl border-2 border-gradient-to-r from-yellow-400/50 via-pink-500/50 to-purple-600/50 max-w-2xl mx-auto relative overflow-hidden animate-[slide-up_1s_ease-out_1.5s_both]">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 via-pink-500/5 to-purple-600/5 animate-pulse"></div>
            <div className="relative z-10">
              <h4 className="text-3xl md:text-4xl font-black text-foreground mb-4 animate-[bounce-text_2s_ease-out_2s_both]">
                🎊 Congratulations to {monthlyWinner.card_name}! 🎊
              </h4>
              <p className="text-xl md:text-2xl text-muted-foreground mb-6 font-bold">
                Created by <span className="text-2xl font-black text-primary bg-gradient-to-r from-yellow-400 to-pink-500 bg-clip-text text-transparent">{monthlyWinner.card_creator}</span> from <span className="text-2xl font-black text-primary bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">{monthlyWinner.card_location}</span>
              </p>
              <div className="flex items-center justify-center gap-3 text-2xl font-bold">
                <Heart className="w-8 h-8 text-red-500 animate-[heart-beat_1.5s_infinite]" />
                <span className="text-foreground bg-gradient-to-r from-red-500 to-pink-500 bg-clip-text text-transparent text-3xl font-black">
                  {monthlyWinner.final_likes} total likes! ❤️
                </span>
              </div>
            </div>
          </div>
          
          <div className="space-y-4 animate-[slide-up_1s_ease-out_2s_both]">
            <Button
              onClick={triggerCelebration}
              className="bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 hover:from-yellow-300 hover:via-pink-400 hover:to-purple-500 hover:scale-110 transition-all duration-300 text-white font-black px-12 py-4 rounded-full shadow-2xl text-xl border-4 border-white/30 relative overflow-hidden group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-[shimmer_3s_infinite] group-hover:animate-[shimmer_1s_infinite]"></div>
              <Sparkles className="w-6 h-6 mr-3 animate-spin relative z-10" />
              <span className="relative z-10">🎉 Celebrate More! 🎉</span>
            </Button>
            
            <p className="text-lg text-muted-foreground font-semibold animate-pulse">
              🎯 Click the card or button for more celebrations! 🎯
            </p>
          </div>
        </div>

        {/* Winner Rewards */}
        <div className="text-center space-y-6 animate-[slide-up_1s_ease-out_2.5s_both]">
          <div className="bg-gradient-to-r from-yellow-400/10 via-pink-500/10 to-purple-600/10 rounded-3xl p-8 max-w-2xl mx-auto border-2 border-yellow-400/30 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 via-pink-500/5 to-purple-600/5 animate-pulse"></div>
            <div className="relative z-10">
              <h4 className="text-2xl md:text-3xl font-black text-foreground mb-4 bg-gradient-to-r from-yellow-400 to-pink-500 bg-clip-text text-transparent">
                🎁 Winner's Special Rewards! 🎁
              </h4>
              <p className="text-lg md:text-xl text-muted-foreground font-bold mb-4">
                As our monthly champion, <span className="text-primary font-black">{monthlyWinner.card_name}</span> will receive:
              </p>
              <div className="space-y-2 text-lg font-semibold">
                <p className="flex items-center justify-center gap-2">
                  <span className="text-2xl">🎴</span>
                  <span className="text-foreground">A special Pixicard pack for FREE!</span>
                </p>
                <p className="flex items-center justify-center gap-2">
                  <span className="text-2xl">🎁</span>
                  <span className="text-foreground">Exclusive winner goodies and surprises!</span>
                </p>
              </div>
            </div>
          </div>
          
        </div>
      </div>
    );
  };

  const weekRange = getWeekDateRange();

  if (mode === 'collection') {
    return (
      <div className="w-full max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Heart className="w-12 h-12 text-accent mr-3 animate-bounce-gentle" />
            <h1 className="text-4xl font-bold text-foreground">
              My Collection
            </h1>
            <Heart className="w-12 h-12 text-accent ml-3 animate-bounce-gentle" />
          </div>
          <p className="text-lg text-muted-foreground font-bold">
            Star cards to build your collection.
          </p>

          {/* Actions (signed in only) */}
          {user && (
            <div className="mt-4 flex items-center justify-center gap-2">
              <Button
                onClick={signOut}
                variant="outline"
                size="sm"
                className="rounded-full"
              >
                Sign out
              </Button>
            </div>
          )}
        </div>

        {/* Search Bar - only for signed in users */}
        {user && (
          <div className="max-w-md mx-auto mt-6">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Search your collection by name, story, or signature..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-full border-2 border-border focus:border-primary transition-colors"
              />
            </div>
          </div>
        )}

        {/* Collection Content */}
        <div className="mt-8">
          {loading ? (
            <div className="text-center py-12">
              <PlayingCardStar className="w-12 h-12 text-accent mx-auto mb-4 animate-spin" />
              <p className="text-muted-foreground">Loading your collection...</p>
            </div>
          ) : !user ? (
            <div className="text-center py-12">
              <Heart className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">
                Sign in to see your collection
              </h3>
              <p className="text-muted-foreground">
                Register or sign in to save and view your starred cards.
              </p>
              <div className="mt-4">
                <Button
                  onClick={() => navigate('/auth')}
                  className="rounded-full bg-hero-gradient text-white hover:scale-105 transition-bounce"
                >
                  Register / Sign in
                </Button>
              </div>
            </div>
          ) : filteredCollectionCards.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-foreground mb-2">
                No starred cards yet
              </h3>
              <p className="text-muted-foreground">
                Explore "This Month" and star your favorites!
              </p>
            </div>
          ) : (
            renderLeaderboardGrid(filteredCollectionCards, true, false)
          )}
        </div>

        {/* Add more cards to your collection */}
        <div className="mt-12 space-y-4">
          <h3 className="text-2xl font-bold text-foreground text-center">Find cards to add</h3>
          <div className="flex items-center justify-center">
            <Popover open={isAddOpen} onOpenChange={setIsAddOpen}>
              <PopoverTrigger asChild>
                <Button className="rounded-full bg-hero-gradient text-white hover:scale-105 transition-bounce">
                  <Search className="w-4 h-4 mr-2" />
                  Search & add a card
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[360px] p-0 bg-background border border-border z-50">
                <Command>
                  <div className="px-2 py-2 border-b border-border">
                    <CommandInput
                      placeholder="Search all cards by name, story, or signature..."
                      value={addSearchQuery}
                      onValueChange={setAddSearchQuery}
                    />
                  </div>
                  <CommandList className="max-h-72 overflow-auto">
                    <CommandEmpty>No cards found.</CommandEmpty>
                    <CommandGroup heading="Available cards">
                      {availableToAddFiltered.map((card) => (
                        <CommandItem
                          key={card.id}
                          value={`${card.name} ${card.signature}`}
                          onSelect={() => {
                            setIsAddOpen(false);
                            setAddSearchQuery("");
                            handleLike(card.id);
                          }}
                          className="flex items-center gap-3"
                        >
                          <img src={card.imageUrl} alt={card.name} className="w-10 h-10 rounded object-cover" />
                          <div className="flex-1 min-w-0">
                            <div className="font-semibold text-foreground truncate">{card.name}</div>
                            <div className="text-xs text-muted-foreground truncate">{card.signature || 'Unknown creator'}</div>
                          </div>
                          <Button size="sm" className="ml-auto rounded-full">Add</Button>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <p className="text-center text-sm text-muted-foreground">Use the dropdown to search the full leaderboard and add favorites to your collection.</p>
        </div>
      </div>
    );
  }
  return (
    <div className="w-full max-w-7xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Trophy className="w-12 h-12 text-accent mr-3 animate-bounce-gentle" />
          <h1 className="text-4xl font-bold text-foreground">
            Leaderboard
          </h1>
          <Trophy className="w-12 h-12 text-accent ml-3 animate-bounce-gentle" />
        </div>
        <p className="text-lg text-muted-foreground font-bold">
          Pick your star! The card with the most stars this month scores an exclusive prize pack!
        </p>
        
      </div>

      {/* Tabs Navigation */}
<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
  <TabsList className="grid w-full grid-cols-3 max-w-lg mx-auto">
    <TabsTrigger value="winner" className="flex items-center gap-2">
      <Crown className="w-4 h-4" />
      Last Month's Winner
    </TabsTrigger>
    <TabsTrigger value="month" className="flex items-center gap-2">
      <Calendar className="w-4 h-4" />
      This Month
    </TabsTrigger>
    <TabsTrigger value="all" className="flex items-center gap-2">
      <Trophy className="w-4 h-4" />
      All Time
    </TabsTrigger>
  </TabsList>

        {/* Search Bar */}
        <div className="max-w-md mx-auto mt-6">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search cards by name, story, or signature..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-2 rounded-full border-2 border-border focus:border-primary transition-colors"
            />
          </div>
        </div>

        {/* Tab Content */}
        <div className="mt-8">
          <TabsContent value="month" className="space-y-6">
            {loading ? (
              <div className="text-center py-12">
                <PlayingCardStar className="w-12 h-12 text-accent mx-auto mb-4 animate-spin" />
                <p className="text-muted-foreground">Loading this month's competition...</p>
              </div>
            ) : filteredMonthSortedCards.length === 0 ? (
              searchQuery ? (
                <div className="text-center py-12">
                  <Search className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    No cards found matching "{searchQuery}"
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Try searching for a different name, story, or signature.
                  </p>
                  <Button 
                    onClick={() => setSearchQuery("")}
                    variant="outline"
                    className="rounded-full"
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Trophy className="w-24 h-24 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    No cards this month yet!
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Be the first to submit a magical character card this month.
                  </p>
                </div>
              )
            ) : (
              renderLeaderboardGrid(filteredMonthSortedCards, true)
            )}
          </TabsContent>

          <TabsContent value="all" className="space-y-6">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Hall of Fame - All Time Champions
              </p>
            </div>
            
            {loading ? (
              <div className="text-center py-12">
                <PlayingCardStar className="w-12 h-12 text-accent mx-auto mb-4 animate-spin" />
                <p className="text-muted-foreground">Loading Hall of Fame...</p>
              </div>
            ) : filteredAllTimeSortedCards.length === 0 ? (
              searchQuery ? (
                <div className="text-center py-12">
                  <Search className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    No cards found matching "{searchQuery}"
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Try searching for a different name, story, or signature.
                  </p>
                  <Button 
                    onClick={() => setSearchQuery("")}
                    variant="outline"
                    className="rounded-full"
                  >
                    Clear Search
                  </Button>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Trophy className="w-24 h-24 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    No cards in the Hall of Fame yet!
                  </h3>
                  <p className="text-muted-foreground mb-6">
                    Be the first to submit a magical character card to the leaderboard.
                  </p>
                </div>
              )
            ) : (
              renderLeaderboardGrid(filteredAllTimeSortedCards, false)
            )}
</TabsContent>


<TabsContent value="winner" className="space-y-6">
  {renderWinnerCelebration()}
</TabsContent>
        </div>
      </Tabs>

      {/* Submit Your Card CTA */}
      <div className="bg-card-gradient rounded-3xl p-8 shadow-magical border border-border text-center">
        <PlayingCardStar className="w-12 h-12 text-accent mx-auto mb-4 animate-sparkle" />
        <h3 className="text-2xl font-bold text-foreground mb-2">
          Create Your Own Legendary Card!
        </h3>
        <p className="text-muted-foreground mb-6">
          Join the hall of fame by creating an amazing character card that everyone will love!
        </p>
        <Button 
          className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold px-8 py-3 rounded-full shadow-magical"
        >
          <Wand2 className="w-5 h-5 mr-2" />
          Start Creating
        </Button>
      </div>

      {/* Discreet Admin Link */}
      <div className="text-center">
        <Link 
          to="/card-downloads" 
          className="text-xs text-muted-foreground hover:text-foreground transition-colors"
        >
          Admin
        </Link>
      </div>
    </div>
  );
}
