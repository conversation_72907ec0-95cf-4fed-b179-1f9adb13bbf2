import React from 'react';
import { useFeaturedCards } from '@/hooks/useImageData';
import { LazyImage } from '@/components/LazyImage';

export function FeaturedCards() {
  const { data: featuredCards, isLoading } = useFeaturedCards();

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="text-muted-foreground">Loading featured cards...</div>
      </div>
    );
  }

  return (
    <div className="mt-16 max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
          ✨ Featured PixiCards from Our Community
        </h2>
        <p className="text-muted-foreground">
          See the amazing characters created by our community that you can order as physical cards!
        </p>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {featuredCards?.map((card) => (
          <div 
            key={card.id} 
            className="group relative bg-card rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            <div className="aspect-square overflow-hidden">
              <LazyImage
                src={card.image_url} 
                alt={card.name}
                size="card"
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
            </div>
            <div className="p-3 bg-gradient-to-t from-background/95 to-background/80 absolute bottom-0 left-0 right-0">
              <h3 className="font-semibold text-sm text-foreground truncate">{card.name}</h3>
              {card.signature && (
                <p className="text-xs text-muted-foreground truncate">
                  {card.signature}
                </p>
              )}
            </div>
          </div>
        )) || []}
      </div>
      
      <div className="text-center mt-6">
        <p className="text-sm text-muted-foreground">
          🎨 These are real PixiCards created by our community • 🃏 Available for physical printing
        </p>
      </div>
    </div>
  );
}