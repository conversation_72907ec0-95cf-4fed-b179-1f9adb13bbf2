import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Card, CardHeader, CardTitle, CardContent } from "./ui/card";
import { Loader2 } from "lucide-react";
import { OrderItem } from "./OrderSummary";
import { calculateOrderTotal, formatPrice } from "@/lib/pricing";
interface CustomerData {
  name: string;
  email: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  specialInstructions: string;
}

interface PrintOrderFormProps {
  orderItems: OrderItem[];
  onOrderSubmitted: () => void;
}

export function PrintOrderForm({ orderItems, onOrderSubmitted }: PrintOrderFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<CustomerData>({
    name: "",
    email: "",
    address: "",
    city: "",
    state: "",
    zip: "",
    country: "United States",
    phone: "",
    specialInstructions: "",
  });

  const handleInputChange = (field: keyof CustomerData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Pricing via shared util: £10 for first 10 cards (per design), then £5 per additional 10
  const calculateTotalPrice = (): number => {
    return calculateOrderTotal(orderItems);
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { supabase } = await import("@/integrations/supabase/client");
      const { data: session } = await supabase.auth.getUser();
      if (!session.user) {
        window.location.href = "/auth";
        return;
      }

      const totalAmount = calculateTotalPrice();
      const totalCards = orderItems.reduce((sum, item) => sum + (item.quantity * 10), 0);

      const { data, error } = await supabase.functions.invoke('create-payment', {
        body: {
          currency: 'gbp',
          items: [{ name: 'PixiCards Print Order', amount: totalAmount, quantity: 1 }],
          orderContext: { type: 'print', totalCards, designs: orderItems.length }
        }
      });

      if (error || !data?.url) throw new Error(error?.message || 'Failed to start checkout');
      window.open(data.url, '_blank');
    } catch (error) {
      console.error("Error starting checkout:", error);
      const { toast } = await import("@/hooks/use-toast");
      toast({ title: "Error", description: "Failed to start checkout.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const totalCards = orderItems.reduce((sum, item) => sum + (item.quantity * 10), 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Checkout</CardTitle>
        <p className="text-sm text-muted-foreground">
          Shipping and recipient details will be collected securely in Stripe Checkout.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="pt-4">
            <div className="space-y-3 mb-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Cards:</span>
                <span className="font-medium">{totalCards} cards</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Cost:</span>
                <span className="text-lg font-bold text-accent">£{calculateTotalPrice().toFixed(2)}</span>
              </div>
              <div className="text-xs text-muted-foreground text-center">
                £10 for the first 10 cards • £5 for each additional 10 of the same design
              </div>
            </div>
            <Button 
              type="submit" 
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 text-white font-bold py-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Redirecting to Checkout...
                </>
              ) : (
                <>
                  <span>Go to Secure Checkout</span>
                  <span className="ml-2 px-2 py-1 bg-white/20 rounded text-sm">
                    £{calculateTotalPrice().toFixed(2)}
                  </span>
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}