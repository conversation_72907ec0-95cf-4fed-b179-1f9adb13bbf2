import { ArrowR<PERSON>, Package, CreditCard, BookOpen } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { FeaturedCards } from "./FeaturedCards";
import individualCardsHero from "@/assets/individual-cards-hero.jpg";
import cardBinderHero from "@/assets/card-binder-hero.jpg";
import pixiCardsLogo from "/lovable-uploads/704c8eb3-aba1-471b-be63-049a15190baf.png";

interface OrderSelectionProps {
  onSelectIndividual: () => void;
  onSelectPacks: () => void;
  onSelectBinders: () => void;
}

export function OrderSelection({ onSelectIndividual, onSelectPacks, onSelectBinders }: OrderSelectionProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-accent/5">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
            Bring Your Collection to Life!
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-4">
            Transform your digital PixiCards into stunning physical collectibles that you can hold, trade, and treasure forever!
          </p>
          <div className="max-w-2xl mx-auto p-4 bg-accent/10 border border-accent/20 rounded-lg">
            <p className="text-lg font-medium text-accent flex items-center justify-center gap-2">
              🎁 Perfect for gifting! 
              <span className="font-normal text-muted-foreground">
                All our physical cards make magical gifts that recipients will treasure forever.
              </span>
            </p>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid md:grid-cols-3 gap-8">
          {/* Pack Option (moved to left) */}
          <Card className="p-0 text-center hover:shadow-2xl transition-all duration-300 border-2 hover:border-accent/50 bg-gradient-to-br from-accent/5 to-accent/10 overflow-hidden group">
            <div className="relative h-56 overflow-hidden bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center">
              <div className="relative">
                <img 
                  src={pixiCardsLogo} 
                  alt="PixiCards Official Logo" 
                  className="h-44 w-auto group-hover:scale-105 transition-transform duration-300 object-contain shadow-2xl rounded-lg border-4 border-accent/30 bg-gradient-to-br from-slate-700 to-slate-800 p-4 transform rotate-2 group-hover:rotate-0 hover-scale"
                />
                <div className="absolute -bottom-2 -right-2 w-full h-full bg-accent/10 rounded-lg border-2 border-accent/20 -z-10 transform rotate-1"></div>
                <div className="absolute -bottom-4 -right-4 w-full h-full bg-accent/5 rounded-lg border border-accent/10 -z-20 transform rotate-0.5"></div>
              </div>
              <div className="absolute top-4 right-4 w-12 h-12 bg-accent/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-accent/30">
                <Package className="w-6 h-6 text-accent" />
              </div>
            </div>
            <div className="p-8">
              <h2 className="text-2xl font-bold mb-4">Mystery Packs</h2>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Each pack contains a carefully curated selection of premium cards and
                <span className="font-semibold text-accent"> includes one of your own creations</span>.
              </p>
              <div className="mb-6 p-4 bg-background/50 rounded-lg border">
                <div className="text-sm text-muted-foreground mb-2">Pack Price</div>
                <div className="text-3xl font-bold text-accent">£10</div>
                <div className="text-sm text-muted-foreground">Curated selection of premium cards</div>
              </div>
              <Button 
                onClick={onSelectPacks}
                className="w-full bg-accent hover:bg-accent/90 text-accent-foreground group/btn"
                size="lg"
              >
                Choose Pack Option
                <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          </Card>

          {/* Individual Cards Option */}
          <Card className="p-0 text-center hover:shadow-2xl transition-all duration-300 border-2 hover:border-primary/50 bg-gradient-to-br from-primary/5 to-primary/10 overflow-hidden group">
            <div className="relative h-48 overflow-hidden">
              <img 
                src={individualCardsHero} 
                alt="Individual PixiCards spread out" 
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent"></div>
              <div className="absolute top-4 right-4 w-12 h-12 bg-primary/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-primary/30">
                <CreditCard className="w-6 h-6 text-primary" />
              </div>
            </div>
            <div className="p-8">
              <h2 className="text-2xl font-bold mb-4">Individual Cards</h2>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                Choose from our collection of unique character cards. Each card features stunning artwork and brings your favorite characters to life.
              </p>
              <div className="mb-6 p-4 bg-background/50 rounded-lg border">
                <div className="text-sm text-muted-foreground mb-2">Pricing</div>
                <div className="font-semibold">£10 for 10 cards</div>
                <div className="text-sm text-accent">£5 for each additional 10 of the same card</div>
              </div>
              <Button 
                onClick={onSelectIndividual}
                className="w-full bg-primary hover:bg-primary/90 text-primary-foreground group/btn"
                size="lg"
              >
                Browse Individual Cards
                <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          </Card>

          {/* Card Binder Option */}
          <Card className="p-0 text-center hover:shadow-2xl transition-all duration-300 border-2 hover:border-secondary/50 bg-gradient-to-br from-secondary/5 to-secondary/10 overflow-hidden group">
            <div className="relative h-48 overflow-hidden">
              <img 
                src={cardBinderHero} 
                alt="Card storage binder with organized cards" 
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-secondary/20 to-transparent"></div>
              <div className="absolute top-4 right-4 w-12 h-12 bg-secondary/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-secondary/30">
                <BookOpen className="w-6 h-6 text-secondary" />
              </div>
            </div>
            <div className="p-8">
              <h2 className="text-2xl font-bold mb-4">Storage Binders</h2>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                High-quality binders designed to protect and organize your card collection. Keep your cards safe and display them beautifully.
              </p>
              <div className="mb-6 p-4 bg-background/50 rounded-lg border">
                <div className="text-sm text-muted-foreground mb-2">Binder Price</div>
                <div className="text-3xl font-bold text-secondary">£20</div>
                <div className="text-sm text-muted-foreground">Premium card storage binder</div>
              </div>
              <Button 
                onClick={onSelectBinders}
                className="w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground group/btn"
                size="lg"
              >
                Buy Card Binders
                <ArrowRight className="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </Button>
            </div>
          </Card>
        </div>

        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground">
            All orders include free shipping within the UK • International shipping available • Gift wrapping options available
          </p>
        </div>

        <FeaturedCards />
      </div>
    </div>
  );
}