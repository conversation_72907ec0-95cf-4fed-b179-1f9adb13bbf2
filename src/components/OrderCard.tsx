import { Plus, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { useState } from "react";

interface OrderCardProps {
  id: string;
  imageUrl: string;
  name: string;
  creator?: string;
  onAdd: (card: { id: string; name: string; creator: string; imageUrl: string }) => void;
}

export function OrderCard({ id, imageUrl, name, creator, onAdd }: OrderCardProps) {
  const handleAdd = () => {
    onAdd({ id, name, creator: creator || "Unknown", imageUrl });
  };

  return (
    <div className="flex items-center justify-between p-3 bg-card border rounded-lg hover:bg-accent/50 transition-colors">
      <div className="flex items-center space-x-3">
        <Dialog>
          <DialogTrigger asChild>
            <div className="relative cursor-pointer group">
              <img 
                src={imageUrl} 
                alt={name}
                className="w-12 h-12 rounded-lg object-cover border group-hover:opacity-80 transition-opacity"
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/50 rounded-lg">
                <Eye className="w-4 h-4 text-white" />
              </div>
            </div>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>{name}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <img 
                src={imageUrl} 
                alt={name}
                className="w-full h-auto rounded-lg object-cover border"
              />
              <p className="text-sm text-muted-foreground text-center">
                Created by {creator || "Unknown"}
              </p>
            </div>
          </DialogContent>
        </Dialog>
        
        <div className="flex-1 min-w-0">
          <h3 className="font-medium text-sm truncate">{name}</h3>
          <p className="text-xs text-muted-foreground truncate">
            by {creator || "Unknown"}
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2 ml-3 shrink-0">
        <Button
          size="sm"
          variant="outline"
          onClick={handleAdd}
          className="text-xs"
        >
          <Plus className="w-3 h-3 mr-1" />
          Add Batch
        </Button>
      </div>
    </div>
  );
}