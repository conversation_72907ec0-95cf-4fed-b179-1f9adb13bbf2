import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CardData {
  id: string;
  name: string;
  backstory?: string | null;
  signature?: string | null;
  image_url: string;
  level?: number;
  power_stat?: number | null;
  magic_stat?: number | null;
  speed_stat?: number | null;
}

interface SafeCardPreviewProps {
  card: CardData;
  onClose: () => void;
}

// Safe function to sanitize and format card data for display
function sanitizeText(text: string | null | undefined): string {
  if (!text) return '';
  
  // Remove any HTML tags and decode entities
  const div = document.createElement('div');
  div.textContent = text;
  return div.textContent || '';
}

function formatOneLineDescription(description: string, maxLength: number = 60): string {
  const sanitized = sanitizeText(description);
  if (sanitized.length <= maxLength) return sanitized;
  return sanitized.substring(0, maxLength - 3) + '...';
}

export function SafeCardPreview({ card, onClose }: SafeCardPreviewProps) {
  const cardName = sanitizeText(card.name);
  const cardDescription = card.backstory 
    ? formatOneLineDescription(card.backstory, 60)
    : '';
  const cardSignature = card.signature ? sanitizeText(card.signature) : '';

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full relative">
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
          className="absolute top-4 right-4 z-10 bg-white/90 hover:bg-white"
        >
          <X className="w-4 h-4" />
        </Button>
        
        <div className="p-8">
          <h3 className="text-lg font-medium mb-4 text-center">Preview: {cardName}</h3>
          
          {/* Safe card preview without dangerouslySetInnerHTML */}
          <div className="bg-white border-2 border-gray-200 rounded-lg p-4 relative">
            <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
              <img 
                src={card.image_url} 
                alt={`Character image for ${cardName}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder.svg';
                }}
              />
            </div>
            
            <h2 className="text-xl font-bold text-center mb-2 text-gray-800">
              {cardName}
            </h2>
            
            {cardDescription && (
              <p className="text-sm text-gray-600 text-center mb-3">
                {cardDescription}
              </p>
            )}
            
            {/* Stats display */}
            {(card.power_stat || card.magic_stat || card.speed_stat) && (
              <div className="grid grid-cols-3 gap-2 mb-3">
                <div className="text-center">
                  <div className="text-xs text-gray-500">POWER</div>
                  <div className="text-lg font-bold">{card.power_stat || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500">MAGIC</div>
                  <div className="text-lg font-bold">{card.magic_stat || 0}</div>
                </div>
                <div className="text-center">
                  <div className="text-xs text-gray-500">SPEED</div>
                  <div className="text-lg font-bold">{card.speed_stat || 0}</div>
                </div>
              </div>
            )}
            
            {card.level && (
              <div className="text-center text-sm text-gray-500 mb-2">
                Level {card.level}
              </div>
            )}
            
            {cardSignature && (
              <p className="text-xs text-gray-500 text-center">
                {cardSignature}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}