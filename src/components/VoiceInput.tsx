import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Wand2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface VoiceInputProps {
  onTranscript: (text: string) => void;
  onAIGenerate: () => void;
  isRecording?: boolean;
  disabled?: boolean;
  showAIWand?: boolean;
  isGenerating?: boolean;
}

export function VoiceInput({ 
  onTranscript, 
  onAIGenerate, 
  isRecording = false, 
  disabled = false,
  showAIWand = true,
  isGenerating = false
}: VoiceInputProps) {
  const [isListening, setIsListening] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const { toast } = useToast();

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      
      recorder.onstart = () => {
        setIsListening(true);
        audioChunksRef.current = [];
      };
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };
      
      recorder.onstop = async () => {
        setIsListening(false);
        stream.getTracks().forEach(track => track.stop());
        
        console.log('Recording stopped, audio chunks:', audioChunksRef.current.length);
        if (audioChunksRef.current.length > 0) {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          console.log('Created audio blob:', audioBlob.size, 'bytes');
          await transcribeAudio(audioBlob);
          audioChunksRef.current = []; // Clear chunks after transcription
        }
      };
      
      setMediaRecorder(recorder);
      recorder.start();
      
      toast({
        title: "🎤 Listening...",
        description: "Speak your text and click stop when done",
      });
    } catch (error) {
      console.error('Error accessing microphone:', error);
      toast({
        title: "❌ Microphone Error",
        description: "Could not access microphone. Please check permissions.",
        variant: "destructive"
      });
    }
  };

  const stopRecording = () => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
  };

  // Helper function to convert ArrayBuffer to base64 in chunks
  const convertToBase64Chunks = (arrayBuffer: ArrayBuffer): string => {
    const uint8Array = new Uint8Array(arrayBuffer);
    const chunkSize = 32768; // 32KB chunks to avoid call stack overflow
    let result = '';
    
    for (let i = 0; i < uint8Array.length; i += chunkSize) {
      const chunk = uint8Array.slice(i, i + chunkSize);
      const chunkString = String.fromCharCode.apply(null, Array.from(chunk));
      result += chunkString;
    }
    
    return btoa(result);
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    try {
      console.log('Starting transcription with audio blob size:', audioBlob.size);
      
      // Convert blob to base64 using chunked approach
      const arrayBuffer = await audioBlob.arrayBuffer();
      console.log('Converting to base64 with chunked approach...');
      const base64Audio = convertToBase64Chunks(arrayBuffer);
      console.log('Base64 conversion completed, length:', base64Audio.length);
      
      console.log('Calling voice-to-text function...');
      const { data, error } = await supabase.functions.invoke('voice-to-text', {
        body: { audio: base64Audio }
      });
      
      console.log('Voice-to-text response:', data, error);
      
      if (error) throw error;
      
      if (data?.text) {
        console.log('Transcribed text:', data.text);
        onTranscript(data.text);
        toast({
          title: "✨ Transcribed!",
          description: "Your voice has been converted to text",
        });
      } else {
        console.log('No text returned from transcription');
        toast({
          title: "⚠️ No Speech Detected",
          description: "Please try speaking more clearly or check your microphone.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast({
        title: "❌ Transcription Failed",
        description: "Could not convert speech to text. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="flex gap-2">
      {showAIWand && (
        <Button
          variant="outline"
          size="icon"
          onClick={onAIGenerate}
          disabled={disabled || isGenerating}
          className={`hover:scale-105 transition-bounce bg-gradient-to-r from-purple-500/10 to-pink-500/10 hover:from-purple-500/20 hover:to-pink-500/20 ${
            isGenerating ? 'animate-pulse' : ''
          }`}
        >
          <Wand2 className={`w-4 h-4 ${isGenerating ? 'animate-spin' : ''}`} />
        </Button>
      )}
    </div>
  );
}