import React from 'react';

interface StatIconProps {
  className?: string;
}

export const PowerIcon: React.FC<StatIconProps> = ({ className = "w-5 h-5" }) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" />
    <path d="M12 6L10.5 10.5L6 11L9.5 14.5L8.5 19L12 16.5L15.5 19L14.5 14.5L18 11L13.5 10.5L12 6Z" fill="white" />
  </svg>
);

export const MagicIcon: React.FC<StatIconProps> = ({ className = "w-5 h-5" }) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M7.5 5.6L5 7L6.4 4.5L5 2L7.5 3.4L10 2L8.6 4.5L10 7L7.5 5.6Z" />
    <path d="M19.5 15.6L17 17L18.4 14.5L17 12L19.5 13.4L22 12L20.6 14.5L22 17L19.5 15.6Z" />
    <path d="M22 2L20 7L15 5L20 10L18 15L23 13L21 18L26 16L24 21L19 19L21 14L16 16L18 11L13 13L15 8L10 10L12 5L7 7L9 2L4 4L6 -1L1 1L3 -4L-2 -2L0 -7L-5 -5L-3 -10L-8 -8L-6 -13L-11 -11L-9 -16L-14 -14L-12 -19L-17 -17L-15 -22L-20 -20L-18 -25L-23 -23Z" />
    <circle cx="12" cy="12" r="8" fill="currentColor" />
    <path d="M12 6L10 10L6 10L9 13L8 17L12 14L16 17L15 13L18 10L14 10L12 6Z" fill="white" />
  </svg>
);

export const SpeedIcon: React.FC<StatIconProps> = ({ className = "w-5 h-5" }) => (
  <svg
    viewBox="0 0 24 24"
    fill="currentColor"
    className={className}
  >
    <path d="M11 4L13 1L15 4L13 7L15 10L13 13L11 10L9 13L7 10L9 7L7 4L9 1L11 4Z" />
    <path d="M4 11L7 9L10 11L7 13L10 15L7 17L4 15L1 17L-1 15L1 13L-1 11L1 9L4 11Z" />
    <path d="M20 11L23 9L26 11L23 13L26 15L23 17L20 15L17 17L15 15L17 13L15 11L17 9L20 11Z" />
    <polygon points="12,2 19,8.5 15,8.5 18,16 6,10 10,10 7,2" fill="currentColor" />
    <polygon points="12,4 17,9 14,9 16,14 8,10 11,10 8,4" fill="white" />
  </svg>
);

interface StatDisplayProps {
  type: 'power' | 'magic' | 'speed';
  value: number;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const StatDisplay: React.FC<StatDisplayProps> = ({ 
  type, 
  value, 
  showLabel = true, 
  size = 'md' 
}) => {
  const icons = {
    power: PowerIcon,
    magic: MagicIcon,
    speed: SpeedIcon
  };

  const colors = {
    power: 'text-red-500',
    magic: 'text-purple-500', 
    speed: 'text-blue-500'
  };

  const labels = {
    power: 'Power',
    magic: 'Magic',
    speed: 'Speed'
  };

  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const Icon = icons[type];

  return (
    <div className="flex flex-col items-center gap-1">
      <div className="flex items-center gap-1">
        <Icon className={`${sizes[size]} ${colors[type]}`} />
        {showLabel && (
          <span className="text-xs font-medium">{labels[type]}</span>
        )}
      </div>
      <span className="text-sm font-bold">{value}</span>
    </div>
  );
};