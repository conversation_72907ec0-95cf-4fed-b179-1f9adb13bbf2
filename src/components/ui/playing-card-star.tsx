import React from 'react';
import { LucideProps } from 'lucide-react';

interface PlayingCardStarProps extends Omit<LucideProps, 'ref'> {}

export const PlayingCardStar: React.FC<PlayingCardStarProps> = ({ 
  size = 24, 
  color = "currentColor", 
  className = "",
  ...props 
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      {/* Playing Card Background */}
      <rect
        x="3"
        y="2"
        width="18"
        height="20"
        rx="3"
        ry="3"
        fill={color}
        stroke={color}
        strokeWidth="0.5"
        opacity="0.9"
      />
      
      {/* Card Inner Border */}
      <rect
        x="4"
        y="3"
        width="16"
        height="18"
        rx="2"
        ry="2"
        fill="none"
        stroke="white"
        strokeWidth="0.5"
        opacity="0.3"
      />
      
      {/* Star Shape */}
      <path
        d="M12 6.5L13.5 10.5H17.5L14.5 12.75L15.5 16.5L12 14.25L8.5 16.5L9.5 12.75L6.5 10.5H10.5L12 6.5Z"
        fill="white"
        stroke="white"
        strokeWidth="0.5"
        opacity="0.95"
      />
    </svg>
  );
};