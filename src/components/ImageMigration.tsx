import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export const ImageMigration = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [hasBrokenImages, setHasBrokenImages] = useState(false);
  const [isChecking, setIsChecking] = useState(true);
  const { toast } = useToast();

  const placeholderImages = [
    'https://images.unsplash.com/photo-1582562124811-c09040d0a901?w=600', // orange tabby cat
    'https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?w=600', // grey kitten
    'https://images.unsplash.com/photo-1472396961693-142e6e269027?w=600', // deer in forest
    'https://images.unsplash.com/photo-1517022812141-23620dba5c23?w=600', // sheep in field
    'https://images.unsplash.com/photo-1485833077593-4278bba3f11f?w=600', // brown deer
    'https://images.unsplash.com/photo-1501286353178-1ec881214838?w=600', // monkey with banana
    'https://images.unsplash.com/photo-1452378174528-3090a4bba7b2?w=600', // horses behind fence
    'https://images.unsplash.com/photo-1498936178812-4b2e558d2937?w=600', // bees in flight
  ];

  // Check for broken images on component mount
  useEffect(() => {
    const checkForBrokenImages = async () => {
      try {
        const { data: userCards } = await supabase
          .from('user_cards')
          .select('id')
          .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

        const { data: leaderboardCards } = await supabase
          .from('leaderboard_cards')
          .select('id')
          .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

        const totalBrokenImages = (userCards?.length || 0) + (leaderboardCards?.length || 0);
        setHasBrokenImages(totalBrokenImages > 0);
      } catch (error) {
        console.error('Error checking for broken images:', error);
        setHasBrokenImages(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkForBrokenImages();
  }, []);

  const runMigration = async () => {
    setIsRunning(true);
    
    try {
      // Get all cards with broken image URLs
      const { data: userCards, error: userError } = await supabase
        .from('user_cards')
        .select('id, image_url')
        .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

      const { data: leaderboardCards, error: leaderboardError } = await supabase
        .from('leaderboard_cards')
        .select('id, image_url')
        .like('image_url', '%oaidalleapiprodscus.blob.core.windows.net%');

      if (userError || leaderboardError) {
        throw userError || leaderboardError;
      }

      let migratedCount = 0;

      // Update user cards
      if (userCards && userCards.length > 0) {
        for (const card of userCards) {
          const randomImage = placeholderImages[Math.floor(Math.random() * placeholderImages.length)];
          const { error } = await supabase
            .from('user_cards')
            .update({ image_url: randomImage })
            .eq('id', card.id);
          
          if (!error) migratedCount++;
        }
      }

      // Update leaderboard cards
      if (leaderboardCards && leaderboardCards.length > 0) {
        for (const card of leaderboardCards) {
          const randomImage = placeholderImages[Math.floor(Math.random() * placeholderImages.length)];
          const { error } = await supabase
            .from('leaderboard_cards')
            .update({ image_url: randomImage })
            .eq('id', card.id);
          
          if (!error) migratedCount++;
        }
      }

      toast({
        title: "Images Updated!",
        description: `Successfully updated ${migratedCount} cards with placeholder images.`,
      });
      
      // Update state to hide the component
      setHasBrokenImages(false);
      
      // Refresh the page to show updated images
      window.location.reload();
      
    } catch (error) {
      console.error('Migration error:', error);
      toast({
        title: "Migration Failed",
        description: "There was an error updating the images. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsRunning(false);
    }
  };

  // Don't render if we're still checking or if there are no broken images
  if (isChecking || !hasBrokenImages) {
    return null;
  }

  return (
    <div className="p-4 border rounded-lg bg-card">
      <h3 className="text-lg font-semibold mb-2">Fix Broken Images</h3>
      <p className="text-sm text-muted-foreground mb-4">
        This will regenerate all images for cards with expired URLs and store them permanently.
      </p>
      <Button 
        onClick={runMigration} 
        disabled={isRunning}
        variant="outline"
      >
        {isRunning ? 'Running Migration...' : 'Fix All Images'}
      </Button>
    </div>
  );
};