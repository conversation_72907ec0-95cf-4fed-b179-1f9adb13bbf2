import { Minus, Plus, X } from "lucide-react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { calculateItemPrice, calculateOrderTotal, formatPrice } from "@/lib/pricing";
export interface OrderItem {
  id: string;
  name: string;
  creator: string;
  imageUrl: string;
  quantity: number;
  price?: number; // Calculated price for this item
}

interface OrderSummaryProps {
  items: OrderItem[];
  onUpdateQuantity: (id: string, quantity: number) => void;
  onRemoveItem: (id: string) => void;
  showPricing?: boolean;
}

export function OrderSummary({ items, onUpdateQuantity, onRemoveItem, showPricing = false }: OrderSummaryProps) {
  const totalCards = items.reduce((sum, item) => sum + (item.quantity * 10), 0);

  // Pricing handled via shared util (first 10 cards £10; each additional 10 £5)
  const calculateTotalPrice = (): number => {
    return calculateOrderTotal(items);
  };

  const handleQuantityChange = (id: string, value: string) => {
    const quantity = Math.max(1, parseInt(value) || 1);
    onUpdateQuantity(id, quantity);
  };
  if (items.length === 0) {
    return (
      <div className="bg-card border rounded-lg p-6 text-center">
        <p className="text-muted-foreground">No cards selected yet. Add some cards to start your order! 🎴</p>
      </div>
    );
  }

  return (
    <div className="bg-card border rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">📦 Your Card Selection</h3>
        <div className="text-sm text-muted-foreground">
          {totalCards} cards total • {items.length} designs
        </div>
      </div>
      
      {showPricing && (
        <div className="mb-4 p-3 bg-accent/10 rounded-lg border border-accent/20">
          <div className="text-sm font-medium text-center">
            💰 Total: <span className="text-lg font-bold text-accent">{formatPrice(calculateTotalPrice())}</span>
          </div>
          <div className="text-xs text-muted-foreground text-center mt-1">
            Batch pricing: first 10 cards £10 • each additional 10 £5
          </div>
        </div>
      )}
      
      <div className="space-y-3">
        {items.map((item) => (
          <div key={item.id} className="p-4 bg-background rounded-lg border">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-4 flex-1 min-w-0">
                <img 
                  src={item.imageUrl} 
                  alt={item.name}
                  className="w-10 h-10 rounded object-cover border flex-shrink-0"
                />
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm truncate">{item.name}</h4>
                  <p className="text-xs text-muted-foreground truncate">by {item.creator}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 flex-shrink-0">
                <div className="flex items-center space-x-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                    className="w-8 h-8 p-0"
                  >
                    <Minus className="w-3 h-3" />
                  </Button>
                  
                  <Input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => handleQuantityChange(item.id, e.target.value)}
                    min="1"
                    className="w-16 h-8 text-center text-sm"
                  />
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                    className="w-8 h-8 p-0"
                  >
                    <Plus className="w-3 h-3" />
                  </Button>
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onRemoveItem(item.id)}
                  className="w-8 h-8 p-0 text-muted-foreground hover:text-destructive"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            {showPricing && (
              <div className="mt-2 pl-14">
                <p className="text-xs font-medium text-accent">
                  {item.quantity} batch{item.quantity > 1 ? 'es' : ''} × 10 cards = {formatPrice(calculateItemPrice(item.quantity))}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {items.length === 0 && (
        <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200 dark:border-blue-800 rounded-lg text-sm text-blue-700 dark:text-blue-300">
          <div className="flex items-center gap-2">
            <span>🎴</span>
            <span>Each card design ordered in batches of 10 • Tiered pricing per design!</span>
          </div>
        </div>
      )}
    </div>
  );
}