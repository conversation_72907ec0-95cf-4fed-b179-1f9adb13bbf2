import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, Zap } from 'lucide-react';

const GenerateStats = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<string>('');
  const [progress, setProgress] = useState({ current: 0, total: 0 });
  const { toast } = useToast();

  const handleGenerateStats = async () => {
    setIsGenerating(true);
    setUpdateStatus('Starting stats generation...');
    setProgress({ current: 0, total: 0 });
    
    try {
      // First, get all cards without stats
      const { data: cards, error: fetchError } = await supabase
        .from('leaderboard_cards')
        .select('id, name, backstory, image_description')
        .or('power_stat.is.null,magic_stat.is.null,speed_stat.is.null')
        .not('backstory', 'is', null)
        .not('backstory', 'eq', '');

      if (fetchError) throw fetchError;

      if (!cards || cards.length === 0) {
        setUpdateStatus('✅ All cards already have stats generated!');
        toast({
          title: "✅ No Updates Needed",
          description: "All cards already have complete stats.",
        });
        return;
      }

      setProgress({ current: 0, total: cards.length });
      setUpdateStatus(`Found ${cards.length} cards without stats. Generating...`);

      let successCount = 0;
      let failureCount = 0;

      for (let i = 0; i < cards.length; i++) {
        const card = cards[i];
        setProgress({ current: i + 1, total: cards.length });
        setUpdateStatus(`Generating stats for "${card.name}" (${i + 1}/${cards.length})...`);

        try {
          // Generate stats using the edge function
          const { data: statsData, error: statsError } = await supabase.functions.invoke('generate-character-stats', {
            body: {
              characterName: card.name,
              imageDescription: card.image_description || 'A character for a trading card game',
              superpower: card.backstory
            }
          });

          if (statsError) {
            console.error(`Stats generation failed for ${card.name}:`, statsError);
            failureCount++;
            continue;
          }

          if (statsData?.power && statsData?.magic && statsData?.speed) {
            // Update the card with generated stats
            const { error: updateError } = await supabase
              .from('leaderboard_cards')
              .update({
                power_stat: statsData.power,
                magic_stat: statsData.magic,
                speed_stat: statsData.speed
              })
              .eq('id', card.id);

            if (updateError) {
              console.error(`Failed to update stats for ${card.name}:`, updateError);
              failureCount++;
            } else {
              successCount++;
            }
          } else {
            console.error(`Invalid stats response for ${card.name}:`, statsData);
            failureCount++;
          }

          // Small delay to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 500));

        } catch (error) {
          console.error(`Error processing ${card.name}:`, error);
          failureCount++;
        }
      }

      const message = `✅ Stats generation completed! Successfully generated stats for ${successCount} cards.`;
      if (failureCount > 0) {
        setUpdateStatus(`${message} ${failureCount} cards failed (using fallback stats).`);
      } else {
        setUpdateStatus(message);
      }

      toast({
        title: "🎉 Stats Generated!",
        description: `Successfully generated stats for ${successCount} cards${failureCount > 0 ? `. ${failureCount} cards used fallback stats.` : '.'}`,
      });

    } catch (error) {
      console.error('Error generating stats:', error);
      setUpdateStatus(`❌ Generation failed: ${error.message}`);
      toast({
        title: "❌ Generation Failed",
        description: "Failed to generate stats. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-4 p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border-2 border-blue-200">
      <div className="flex items-center gap-2">
        <Zap className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-bold text-blue-800">Generate Character Stats</h3>
      </div>
      
      <p className="text-sm text-blue-700">
        Generate Power, Magic, and Speed stats for all cards that don't have them yet. Stats are based on each character's name, appearance, and superpower.
      </p>
      
      <Button 
        onClick={handleGenerateStats}
        disabled={isGenerating}
        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold px-6 py-3 rounded-full"
      >
        {isGenerating ? (
          <>
            <Zap className="w-4 h-4 mr-2 animate-pulse" />
            Generating...
          </>
        ) : (
          'Generate All Stats'
        )}
      </Button>
      
      {progress.total > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-blue-700">
            <span>Progress:</span>
            <span>{progress.current}/{progress.total}</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(progress.current / progress.total) * 100}%` }}
            />
          </div>
        </div>
      )}
      
      {updateStatus && (
        <div className="mt-4 p-3 bg-white rounded-lg border border-blue-200">
          <p className="text-sm font-mono">{updateStatus}</p>
        </div>
      )}
    </div>
  );
};

export default GenerateStats;