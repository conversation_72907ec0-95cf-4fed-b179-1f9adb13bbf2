import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Download, Edit3, Trophy, Share, Star } from "lucide-react";
import { PlayingCardStar } from "./ui/playing-card-star";
import { useToast } from "@/hooks/use-toast";
import { ShippingForm } from "./ShippingForm";
import { VoiceInput } from "./VoiceInput";
import { supabase } from "@/integrations/supabase/client";
import confetti from "canvas-confetti";
import pixieLogo from "@/assets/pixie-logo.png";
import { LazyImage } from "./LazyImage";
import { StatDisplay } from "./ui/stat-icons";

interface CharacterCardProps {
  imageUrl: string;
  onSave?: (cardData: CardData) => void;
  onLike?: () => void;
  onPrint?: (cardData: CardData) => void;
  onDelete?: () => void;
  isEditable?: boolean;
  isCurrentCardSaved?: boolean;
  likes?: number; // Still using "likes" internally for database compatibility
  isLiked?: boolean;
  level?: number;
  initialData?: Partial<CardData>;
  maxWidth?: string;
  imageDescription?: string;
  showDeleteButton?: boolean; // Control delete button visibility
  size?: 'default' | 'wide';
  isPreview?: boolean;
}

export interface CardData {
  name: string;
  backstory: string;
  signature: string;
  imageUrl: string;
  level?: number;
  power_stat?: number;
  magic_stat?: number;
  speed_stat?: number;
}

export function CharacterCard({ 
  imageUrl, 
  onSave, 
  onLike, 
  onPrint, 
  onDelete,
  isEditable = false,
  isCurrentCardSaved = false,
  likes = 0,
  isLiked = false,
  level,
  initialData,
  maxWidth = "max-w-sm sm:max-w-md md:max-w-lg",
  imageDescription = "",
  showDeleteButton = true, // Default to true for backwards compatibility
  size = 'default',
  isPreview = false
}: CharacterCardProps) {
  const [isEditing, setIsEditing] = useState(isEditable);
  const [name, setName] = useState(initialData?.name || "");
  const [backstory, setBackstory] = useState(initialData?.backstory || "");
  const [characterStats, setCharacterStats] = useState<{power: number, magic: number, speed: number} | null>(
    initialData?.power_stat && initialData?.magic_stat && initialData?.speed_stat 
      ? { power: initialData.power_stat, magic: initialData.magic_stat, speed: initialData.speed_stat }
      : null
  );
  const [isGeneratingStats, setIsGeneratingStats] = useState(false);
  const [creatorName, setCreatorName] = useState("");
  const [creatorLocation, setCreatorLocation] = useState("");
  
  // Check if signature is already provided from ImageGenerator
  const hasProvidedSignature = initialData?.signature && initialData.signature.trim() !== "";
  const cardLevel = level || initialData?.level || 1;
  
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [shareEmail, setShareEmail] = useState("");
  const [sharePhone, setSharePhone] = useState("");
  const [isGeneratingStory, setIsGeneratingStory] = useState(false);
  const [validationMessage, setValidationMessage] = useState("");
  const { toast } = useToast();

  // Auto-generate stats guard: remember last inputs used
  const lastStatsKeyRef = useRef<string | null>(null);

  const triggerStatsIfReady = async () => {
    const n = name.trim();
    const b = backstory.trim();
    if (!n || !b || isGeneratingStats) return;
    const key = `${n}|${b}`;
    if (lastStatsKeyRef.current === key && characterStats) return;
    await handleGenerateStats();
    lastStatsKeyRef.current = key;
  };

  // Kid-friendly content filter
  const inappropriateWords = [
    'hate', 'kill', 'die', 'death', 'stupid', 'dumb', 'idiot', 'ugly', 'fat', 'loser',
    'shut up', 'damn', 'hell', 'crap', 'suck', 'worst', 'terrible', 'awful', 'horrible',
    'monster', 'evil', 'villain', 'bad guy', 'scary', 'mean', 'angry', 'mad', 'fight'
  ];

  const isContentAppropriate = (text: string): boolean => {
    const lowerText = text.toLowerCase();
    return !inappropriateWords.some(word => lowerText.includes(word));
  };

  const handleNameChange = (value: string) => {
    if (!isContentAppropriate(value)) {
      toast({
        title: "🌟 Let's keep it magical!",
        description: "Please choose a friendlier name for your character.",
        variant: "destructive"
      });
      return;
    }
    setName(value);
  };

  const handleSave = async () => {
    // Validate that both name and backstory are provided
    if (!name.trim()) {
      setValidationMessage("🌟 Please enter a character name!");
      setTimeout(() => setValidationMessage(""), 5000);
      return;
    }

    if (!backstory.trim()) {
      setValidationMessage("✨ Please add a character description!");
      setTimeout(() => setValidationMessage(""), 5000);
      return;
    }

    // Sign-in is optional for saving to the public leaderboard; auth is handled on the collection page

    // Ensure stats exist before saving
    let stats = characterStats as { power: number; magic: number; speed: number } | null;
    if (!stats) {
      setIsGeneratingStats(true);
      try {
        const { data, error } = await supabase.functions.invoke('generate-character-stats', {
          body: {
            characterName: name,
            imageDescription: imageDescription || 'A character for a trading card game',
            superpower: backstory
          }
        });
        if (error) throw error;
        if (data?.power && data?.magic && data?.speed) {
          stats = { power: data.power, magic: data.magic, speed: data.speed };
          setCharacterStats(stats);
        } else {
          // Fallback stats if API returns invalid payload
          stats = { power: 60, magic: 50, speed: 40 };
          setCharacterStats(stats);
        }
      } catch (err) {
        console.error('Error generating stats on save:', err);
        // Use fallback on any failure
        stats = { power: 60, magic: 50, speed: 40 };
        setCharacterStats(stats);
      } finally {
        setIsGeneratingStats(false);
      }
    }

    // Use provided signature if available; do not ask for name/city anymore
    const signature = hasProvidedSignature 
      ? initialData?.signature || ""
      : "";
    
    if (onSave && stats) {
      onSave({ 
        name, 
        backstory, 
        signature, 
        imageUrl, 
        level: cardLevel,
        power_stat: stats.power,
        magic_stat: stats.magic,
        speed_stat: stats.speed
      });
    }
    
    // Trigger confetti
    confetti({
      particleCount: 50,
      spread: 60,
      origin: { y: 0.7 }
    });
    
    setIsEditing(false);
    setShowShareOptions(true);
    
    // Disable editing after successful save if this is a new card
    if (isEditable && onSave) {
      // The parent should handle setting isEditable to false after save
    }
  };

  const handleVoiceTranscript = (text: string) => {
    setBackstory(text);
  };

  const handleAIGenerateStory = async () => {
    // Check if character name is provided before generating story
    if (!name.trim()) {
      setValidationMessage("🌟 Please enter a character name first!");
      setTimeout(() => setValidationMessage(""), 5000);
      return;
    }

    setIsGeneratingStory(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-story', {
        body: { 
          characterName: name,
          imageDescription: imageDescription || "fantasy character" 
        }
      });

      if (error) throw error;

      if (data?.superpower) {
        setBackstory(data.superpower);
        toast({
          title: "✨ Superpower Generated!",
          description: "AI has created a magical superpower for your character!",
        });
        
        // Generate stats after superpower is finalized
        await handleGenerateStats(data.superpower);
      }
    } catch (error) {
      console.error('Error generating story:', error);
      toast({
        title: "❌ Generation Failed",
        description: "Could not generate story. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingStory(false);
    }
  };

  const handleGenerateStats = async (superpower?: string) => {
    if (!name.trim()) {
      setValidationMessage("🌟 Please enter a character name first!");
      setTimeout(() => setValidationMessage(""), 5000);
      return;
    }

    const backstoryToUse = superpower || backstory;
    if (!backstoryToUse?.trim()) {
      setValidationMessage("✨ Please generate a superpower first!");
      setTimeout(() => setValidationMessage(""), 5000);
      return;
    }

    setIsGeneratingStats(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('generate-character-stats', {
        body: {
          characterName: name,
          imageDescription: imageDescription || 'A character for a trading card game',
          superpower: backstoryToUse
        }
      });

      if (error) throw error;

      if (data?.power && data?.magic && data?.speed) {
        setCharacterStats({
          power: data.power,
          magic: data.magic,
          speed: data.speed
        });
        
        if (data.generated) {
          toast({
            title: "⚡ Stats Generated!",
            description: "AI has created balanced stats for your character!",
          });
        } else {
          toast({
            title: "📊 Default Stats Applied",
            description: "Using default stats (generation failed)",
            variant: "destructive"
          });
        }
      } else {
        throw new Error('Invalid stats response');
      }
    } catch (error) {
      console.error('Error generating stats:', error);
      // Use fallback stats
      setCharacterStats({ power: 60, magic: 50, speed: 40 });
      toast({
        title: "📊 Default Stats Applied", 
        description: "Failed to generate stats. Using defaults.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingStats(false);
      if (name.trim() && backstoryToUse?.trim()) {
        lastStatsKeyRef.current = `${name.trim()}|${backstoryToUse.trim()}`;
      }
    }
  };

  const handleShare = async () => {
    const shareData = {
      title: `Check out my PixiCard: ${name}`,
      text: `I created this amazing character card called "${name}"! ${backstory}`,
      url: window.location.href
    };

    // Try Web Share API first
    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData);
        return; // Success, exit early
      } catch (error) {
        console.log('Share cancelled or failed:', error);
        // Continue to fallback
      }
    }

    // Fallback - copy to clipboard
    try {
      const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
      await navigator.clipboard.writeText(shareText);
      toast({
        title: "📋 Copied to clipboard!",
        description: "Share text has been copied. Paste it wherever you'd like to share!",
      });
    } catch (error) {
      // Final fallback - show share text in a dialog
      const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
      prompt("Copy this text to share:", shareText);
      toast({
        title: "📋 Share Text Ready",
        description: "Copy the text from the dialog to share your card!",
      });
    }
  };


  return (
    <div className={`w-full ${size === 'wide' ? 'max-w-none' : maxWidth} mx-auto space-y-3 sm:space-y-4`}>
      {/* Trading Card - Portrait Format */}
      <div className={`relative z-0 overflow-visible bg-gradient-to-br from-pink-400 via-purple-500 to-cyan-400 p-1 sm:p-2 rounded-2xl sm:rounded-3xl shadow-2xl transform motion-safe:md:hover:rotate-1 motion-safe:md:hover:scale-105 transition-all duration-300 ${size === 'wide' ? 'w-full' : 'max-w-xs sm:max-w-sm'} mx-auto min-h-[603px] sm:min-h-[742px]`}>
        {/* Sparkly border effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-yellow-300 via-pink-300 to-purple-300 rounded-2xl sm:rounded-3xl opacity-75 blur-sm animate-pulse pointer-events-none z-0"></div>
        
        {/* Inner card with fun kid-friendly styling */}
        <div className="relative bg-gradient-to-br from-yellow-100 via-pink-50 to-blue-100 rounded-xl sm:rounded-2xl overflow-hidden border-2 sm:border-4 border-white shadow-inner">
          
          {/* Card Header with Clean PixiCard Text */}
          <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-blue-500 px-4 sm:px-6 py-4 sm:py-6 relative overflow-hidden">
            <div className="relative flex justify-center items-center">
              <span className="text-2xl sm:text-3xl font-black text-white drop-shadow-lg tracking-wider">PIXICARDS</span>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-3 sm:p-4">
            {/* Character Name Header - Mobile Optimized */}
            <div className="text-center mb-3 bg-gradient-to-r from-yellow-200 via-pink-200 to-purple-200 rounded-lg sm:rounded-xl p-3 sm:p-4 border-2 border-yellow-300 shadow-lg">
              {isEditing ? (
                <Input
                  value={name}
                  onChange={(e) => handleNameChange(e.target.value)}
                  onBlur={triggerStatsIfReady}
                  placeholder="Character Name"
                  className="text-center text-base sm:text-lg font-card font-black bg-transparent border-none shadow-none text-purple-800 placeholder-purple-400 p-0 min-h-[40px]"
                  maxLength={30}
                />
              ) : (
                <h3 className="text-base sm:text-lg font-card font-black text-purple-800 tracking-wide drop-shadow-sm min-h-[40px] flex items-center justify-center">
                  {name || "🌟 Magical Friend 🌟"}
                </h3>
              )}
            </div>

            {/* Main Character Image - Mobile Optimized */}
            <div className="relative mb-3">
              {isPreview && (
                <div className="absolute top-2 left-2 z-10 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-[10px] sm:text-xs px-2 py-1 rounded-md border border-white/20 shadow-lg">
                  Preview
                </div>
              )}
              <div className="aspect-[4/5] sm:aspect-[3/4] rounded-xl sm:rounded-2xl overflow-hidden bg-gradient-to-br from-pink-300 to-blue-300 p-1 sm:p-2 border-2 sm:border-4 border-white shadow-xl">
                <LazyImage
                  src={imageUrl} 
                  alt={name || "Generated character"}
                  className="w-full h-full object-cover rounded-lg sm:rounded-xl"
                  size="card"
                />
              </div>
              
            </div>

            {/* Character Superpower Box - Mobile Optimized */}
            <div className="bg-gradient-to-br from-white to-blue-50 border-2 border-blue-200 rounded-lg sm:rounded-xl p-3 mb-3 shadow-lg">
              {isEditing ? (<>
                <div className="relative">
                  <Textarea
                    value={backstory}
                    onChange={(e) => setBackstory(e.target.value)}
                    onBlur={triggerStatsIfReady}
                    placeholder="What's your superpower? ⚡"
                    className="text-xs sm:text-sm font-card bg-white border-2 border-purple-200 focus:border-pink-400 rounded-lg resize-none text-purple-800 placeholder-purple-400 min-h-[40px] sm:min-h-[50px] p-2 sm:p-3 leading-relaxed"
                    maxLength={80}
                  />
                  <div className="absolute top-2 right-2">
                    <VoiceInput 
                      onTranscript={handleVoiceTranscript}
                      onAIGenerate={handleAIGenerateStory}
                      disabled={isGeneratingStory}
                      showAIWand={true}
                      isGenerating={isGeneratingStory}
                    />
                  </div>
                  {/* Validation Message Popup */}
                  {validationMessage && (
                    <div className="absolute top-12 right-2 z-50 bg-red-500 text-white px-3 py-2 rounded-lg text-xs font-bold shadow-lg animate-pulse border border-white max-w-[200px]">
                      {validationMessage}
                    </div>
                  )}
                </div>

                {/* Character Stats Display (visible during editing too) */}
                {characterStats ? (
                  <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg mt-2">
                    <div className="flex justify-center items-center divide-x divide-gray-300">
                      <div className="flex flex-col items-center px-4">
                        <div className="flex items-center gap-1 mb-1">
                          <span className="text-red-500">⭐</span>
                          <span className="text-xs font-medium text-red-600">Power</span>
                        </div>
                        <span className="text-lg font-bold text-gray-800">{characterStats.power}</span>
                      </div>
                      <div className="flex flex-col items-center px-4">
                        <div className="flex items-center gap-1 mb-1">
                          <span className="text-purple-500">🔮</span>
                          <span className="text-xs font-medium text-purple-600">Magic</span>
                        </div>
                        <span className="text-lg font-bold text-gray-800">{characterStats.magic}</span>
                      </div>
                      <div className="flex flex-col items-center px-4">
                        <div className="flex items-center gap-1 mb-1">
                          <span className="text-blue-500">💨</span>
                          <span className="text-xs font-medium text-blue-600">Speed</span>
                        </div>
                        <span className="text-lg font-bold text-gray-800">{characterStats.speed}</span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg mt-2">
                    <div className="text-xs text-purple-700 text-center font-medium">
                      Add your one-line superpower to unlock stats ⚡
                    </div>
                  </div>
                )}
              </>) : (
                <>
                  {/* Character Stats Display - First, on top */}
                  {characterStats ? (
                    <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg">
                      <div className="flex justify-center items-center divide-x divide-gray-300">
                        <div className="flex flex-col items-center px-4">
                          <div className="flex items-center gap-1 mb-1">
                            <span className="text-red-500">⭐</span>
                            <span className="text-xs font-medium text-red-600">Power</span>
                          </div>
                          <span className="text-lg font-bold text-gray-800">{characterStats.power}</span>
                        </div>
                        <div className="flex flex-col items-center px-4">
                          <div className="flex items-center gap-1 mb-1">
                            <span className="text-purple-500">🔮</span>
                            <span className="text-xs font-medium text-purple-600">Magic</span>
                          </div>
                          <span className="text-lg font-bold text-gray-800">{characterStats.magic}</span>
                        </div>
                        <div className="flex flex-col items-center px-4">
                          <div className="flex items-center gap-1 mb-1">
                            <span className="text-blue-500">💨</span>
                            <span className="text-xs font-medium text-blue-600">Speed</span>
                          </div>
                          <span className="text-lg font-bold text-gray-800">{characterStats.speed}</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg">
                      <div className="text-xs text-purple-700 text-center font-medium">
                        Add your one-line superpower to unlock stats ⚡
                      </div>
                    </div>
                  )}

                  {/* Description/One-liner - Second, below stats */}
                  <div className="bg-gradient-to-br from-white to-blue-50 border-2 border-blue-200 rounded-lg p-2 mb-2 shadow-lg">
                    <p className="text-xs font-card text-purple-800 text-center leading-relaxed bg-white rounded-lg p-2 border border-purple-100 line-clamp-3">
                      {backstory || "This magical friend loves adventures and spreading joy! ✨🌈"}
                    </p>
                  </div>
                </>
              )}
              
              {/* Stats during generation state */}
              {isGeneratingStats && (
                <div className="bg-gradient-to-r from-white to-blue-50 border-2 border-blue-200 rounded-lg p-3 mb-2 shadow-lg">
                  <div className="text-xs text-gray-500 italic text-center">Generating stats...</div>
                </div>
              )}
        </div>

            {/* Creator Info - Mobile Optimized */}
            {/* Creator Info removed (legacy Safari issue) */}

            {/* Card Footer - Mobile Optimized */}
            <div className="bg-gradient-to-r from-purple-600 via-pink-500 to-orange-400 text-white rounded-lg sm:rounded-xl p-3 text-center shadow-lg">
              <div className="text-xs sm:text-sm font-card font-bold text-white drop-shadow leading-relaxed min-h-[40px] flex items-center justify-center">
                {hasProvidedSignature ? initialData?.signature :
                  (isEditing && (creatorName || creatorLocation) ? 
                    `Created by ${creatorName || 'you'} from ${creatorLocation || 'your city'}` : 
                    "✨ Create your magical character card ✨"
                  )
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons - Mobile Optimized */}
      {(isEditing || isEditable || onPrint || onLike) && (
        <div className="relative z-10 bg-card-gradient rounded-2xl p-4 sm:p-5 shadow-lg border border-border">
          {isEditing ? (
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {isCurrentCardSaved ? (
                <div className="flex items-center justify-center py-3 px-8 bg-green-100 text-green-700 rounded-full border-2 border-green-300 min-h-[48px]">
                  <PlayingCardStar className="w-5 h-5 mr-2" />
                  <span className="font-bold">Card Already Saved!</span>
                </div>
              ) : onSave ? (
                <Button 
                  onClick={handleSave}
                  className="bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold rounded-full px-6 py-2 min-h-[44px] text-sm"
                >
                  <PlayingCardStar className="w-5 h-5 mr-2" />
                  Save Card
                </Button>
              ) : null}
              {/* Only show delete button when explicitly enabled and available */}
              {showDeleteButton && onDelete && (
                <Button 
                  variant="outline"
                  onClick={onDelete}
                  className="rounded-full px-6 py-2 min-h-[44px] text-sm"
                >
                  Delete card
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {/* Edit Action */}
              {isEditable && (
                <Button 
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                  className="rounded-full hover:scale-105 transition-bounce flex flex-col items-center py-3 h-auto min-h-[50px]"
                >
                  <Edit3 className="w-5 h-5 mb-1" />
                  <span className="text-sm font-medium">Edit</span>
                </Button>
              )}
              
              {/* Print button removed - functionality moved to order page */}
              
              {/* Like Action - only show for leaderboard cards */}
              {onLike && (
                <Button 
                  variant={isLiked ? "default" : "outline"}
                  onClick={onLike}
                  className={`rounded-full hover:scale-105 transition-bounce flex flex-col items-center py-3 h-auto col-span-full min-h-[50px] ${isLiked ? 'bg-hero-gradient text-white border-transparent' : ''}`}
                >
                  <Star className={`w-5 h-5 mb-1 ${isLiked ? 'fill-current' : ''}`} />
                  <span className="text-sm font-medium">{likes} stars</span>
                </Button>
              )}
            </div>
          )}

          {/* Character count helpers */}
          {isEditing && backstory && (
            <div className="mt-3 text-sm text-muted-foreground text-center">
              <p>{backstory.length}/80 characters</p>
            </div>
          )}
        </div>
      )}

    </div>
  );
}
