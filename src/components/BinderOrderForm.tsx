import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';
import { ArrowLeft, Minus, Plus, ShoppingCart, BookOpen } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface BinderOrderFormProps {
  onOrderSubmitted: () => void;
  onBack: () => void;
}

export function BinderOrderForm({ onOrderSubmitted, onBack }: BinderOrderFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [binderQuantity, setBinderQuantity] = useState(1);
  const [selectedColor, setSelectedColor] = useState('yellow');
  
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    country: 'United Kingdom',
    phone: '',
    specialInstructions: ''
  });

  const BINDER_PRICE = 20; // £20 per binder

  const binderColors = [
    { id: 'yellow', name: 'Yellow', image: '/lovable-uploads/b7acc01e-e414-4e7a-854e-f6753198cd42.png' },
    { id: 'red', name: 'Red', image: '/lovable-uploads/d643b34c-0deb-42ed-be39-8cb73532b442.png' },
    { id: 'teal', name: 'Teal', image: '/lovable-uploads/aaff01ad-d1c7-4c76-a98c-39b8dbc6d2a0.png' },
  ];

  const handleInputChange = (field: string, value: string) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleQuantityChange = (delta: number) => {
    setBinderQuantity(prev => Math.max(1, prev + delta));
  };

  const calculateTotal = () => {
    return binderQuantity * BINDER_PRICE;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        window.location.href = '/auth';
        return;
      }

      const { data, error: fnError } = await supabase.functions.invoke('create-payment', {
        body: {
          currency: 'gbp',
          items: [{
            name: `PixiCards Storage Binder (${binderColors.find(c => c.id === selectedColor)?.name})`,
            amount: calculateTotal(),
            quantity: 1,
            image: binderColors.find(c => c.id === selectedColor)?.image,
          }],
          orderContext: { type: 'binder', color: selectedColor, qty: binderQuantity }
        }
      });

      if (fnError || !data?.url) throw new Error(fnError?.message || 'Failed to start checkout');
      window.open(data.url, '_blank');

      toast({ title: 'Redirecting to Stripe Checkout...' });
    } catch (error) {
      console.error('Error starting binder checkout:', error);
      toast({ title: 'Checkout Failed', description: 'Could not start checkout.', variant: 'destructive' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          onClick={onBack}
          className="hover:scale-105 transition-bounce"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Order Options
        </Button>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-3xl sm:text-4xl font-bold bg-hero-gradient bg-clip-text text-transparent flex items-center justify-center gap-2">
          <BookOpen className="w-8 h-8 text-secondary" />
          Order Card Binders
        </h1>
        <p className="text-muted-foreground">High-quality storage binders for your PixiCards collection</p>
      </div>

      <div className="max-w-2xl mx-auto grid gap-6">
        {/* Binder Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-secondary" />
              Card Binder Selection
            </CardTitle>
            <CardDescription>
              Professional storage binders to organize and protect your PixiCards
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Color Selection */}
            <div>
              <h4 className="font-semibold mb-3">Choose Your Binder Color</h4>
              <div className="grid grid-cols-3 gap-4">
                {binderColors.map((color) => (
                  <div 
                    key={color.id}
                    className={`cursor-pointer border-2 rounded-lg p-2 transition-all ${
                      selectedColor === color.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedColor(color.id)}
                  >
                    <img 
                      src={color.image} 
                      alt={`${color.name} binder`}
                      className="w-full h-32 object-contain mb-2"
                    />
                    <p className="text-center font-medium">{color.name}</p>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Quantity Selection */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-semibold">PixiCards Storage Binder</h3>
                <p className="text-sm text-muted-foreground">
                  {binderColors.find(c => c.id === selectedColor)?.name} binder
                </p>
                <p className="font-bold text-secondary">£{BINDER_PRICE}</p>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(-1)}
                  disabled={binderQuantity <= 1}
                >
                  <Minus className="w-4 h-4" />
                </Button>
                <span className="w-8 text-center font-semibold">{binderQuantity}</span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(1)}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <Separator />
            
            <div className="flex justify-between items-center font-bold text-lg">
              <span>Total:</span>
              <span className="text-secondary">£{calculateTotal()}</span>
            </div>
          </CardContent>
        </Card>

        {/* Checkout CTA - Shipping collected at Stripe */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="w-5 h-5 text-primary" />
              Secure Checkout
            </CardTitle>
            <CardDescription>
              Shipping address and recipient details will be collected on Stripe Checkout.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="flex justify-between items-center font-bold text-lg">
                <span>Total:</span>
                <span className="text-secondary">£{calculateTotal()}</span>
              </div>
              <Button 
                type="submit" 
                className="w-full bg-hero-gradient hover:scale-105 transition-bounce text-white font-bold py-4 rounded-full shadow-magical"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Redirecting...' : `Go to Secure Checkout (£${calculateTotal()})`}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}