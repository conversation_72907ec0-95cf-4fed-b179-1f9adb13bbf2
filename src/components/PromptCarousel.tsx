
import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { PlayingCardStar } from "./ui/playing-card-star";
import { Button } from "@/components/ui/button";

const promptIdeas = [
  "a rainbow dragon flying through cotton candy clouds",
  "a space fairy with glittering wings and star dust",
  "a robot chef making magical cupcakes",
  "a friendly monster wearing a superhero cape",
  "a crystal unicorn in an enchanted forest",
  "a pirate cat sailing on a ship made of cheese",
  "a wizard penguin casting ice magic spells",
  "a butterfly princess in a flower kingdom",
  "a steampunk fox inventor with brass goggles",
  "a mermaid knight riding a seahorse",
  "a ninja squirrel with throwing acorns",
  "a phoenix made of autumn leaves",
  "a cosmic owl with galaxy patterns in its feathers",
  "a gentle giant holding a bouquet of tiny stars",
  "a clockwork ballerina dancing on music notes",
  "a garden gnome wizard with mushroom magic",
  "a sugar skull day of the dead celebration",
  "a lighthouse keeper ghost with a lantern",
  "a dreamcatcher weaver spider with rainbow silk",
  "a constellation bear made of twinkling stars"
];

interface PromptCarouselProps {
  onSelectPrompt: (prompt: string) => void;
}

export function PromptCarousel({ onSelectPrompt }: PromptCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % promptIdeas.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const nextPrompt = () => {
    setCurrentIndex((prev) => (prev + 1) % promptIdeas.length);
  };

  const prevPrompt = () => {
    setCurrentIndex((prev) => (prev - 1 + promptIdeas.length) % promptIdeas.length);
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto px-4">
      <div className="bg-card-gradient rounded-2xl py-1.5 px-3 sm:py-2.5 sm:px-4 shadow-magical border border-border">
        <div className="flex items-center justify-center mb-4">
          <PlayingCardStar className="w-5 h-5 sm:w-6 sm:h-6 text-accent mr-2 animate-sparkle" />
          <h2 className="text-lg sm:text-xl font-bold text-foreground text-center">
            Magical Card Ideas
          </h2>
          <PlayingCardStar className="w-5 h-5 sm:w-6 sm:h-6 text-accent ml-2 animate-sparkle" />
        </div>
        
        <div className="relative min-h-[100px] sm:h-20 flex items-center justify-center">
          <Button
            variant="outline"
            size="icon"
            onClick={prevPrompt}
            className="absolute left-2 z-10 bg-background/90 hover:bg-background transition-bounce h-10 w-10 sm:h-8 sm:w-8"
          >
            <ChevronLeft className="w-5 h-5 sm:w-4 sm:h-4" />
          </Button>
          
          <div className="text-center px-14 sm:px-12">
            <p className="text-sm sm:text-base font-medium text-foreground leading-relaxed animate-slide-in-up">
              "{promptIdeas[currentIndex]}"
            </p>
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={nextPrompt}
            className="absolute right-2 z-10 bg-background/90 hover:bg-background transition-bounce h-10 w-10 sm:h-8 sm:w-8"
          >
            <ChevronRight className="w-5 h-5 sm:w-4 sm:h-4" />
          </Button>
        </div>
        
        <div className="flex justify-center mt-4">
          <Button 
            onClick={() => onSelectPrompt(promptIdeas[currentIndex])}
            className="bg-hero-gradient hover:scale-105 transition-bounce shadow-magical text-white font-bold px-6 sm:px-8 py-3 rounded-full text-sm sm:text-base min-h-[48px]"
          >
            <PlayingCardStar className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
            Create This Card!
          </Button>
        </div>
        
        <div className="flex justify-center mt-4 space-x-2">
          {promptIdeas.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 sm:w-2 sm:h-2 rounded-full transition-all min-h-[32px] min-w-[32px] sm:min-h-0 sm:min-w-0 ${
                index === currentIndex 
                  ? 'bg-primary scale-125' 
                  : 'bg-muted hover:bg-muted-foreground/50'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
