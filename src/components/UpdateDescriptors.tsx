import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, RefreshCw } from 'lucide-react';

const UpdateDescriptors = () => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<string>('');
  const { toast } = useToast();

  const handleUpdateDescriptors = async () => {
    setIsUpdating(true);
    setUpdateStatus('Starting update process...');
    
    try {
      const { data, error } = await supabase.functions.invoke('update-long-descriptors', {
        body: {}
      });

      if (error) {
        throw error;
      }

      if (data?.success) {
        const updatedLeaderboard = (typeof data.updated_leaderboard === 'number') ? data.updated_leaderboard : 0;
        const updatedUser = (typeof data.updated_user === 'number') ? data.updated_user : 0;
        const total = (typeof data.updated === 'number')
          ? data.updated
          : (updatedLeaderboard + updatedUser);

        setUpdateStatus(
          total > 0
            ? `✅ Update completed! Updated ${updatedLeaderboard} leaderboard cards and ${updatedUser} user cards. (Total: ${total})`
            : (data.message || '✅ Update completed! No cards needed changes.')
        );
        toast({
          title: "🎉 Descriptors Updated!",
          description: total > 0 ? `Successfully regenerated ${total} one-liners.` : 'No changes were required.',
        });
      } else {
        throw new Error(data?.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error updating descriptors:', error);
      setUpdateStatus(`❌ Update failed: ${error.message}`);
      toast({
        title: "❌ Update Failed",
        description: "Failed to update descriptors. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-4 p-6 bg-gradient-to-br from-yellow-50 to-pink-50 rounded-xl border-2 border-purple-200">
      <div className="flex items-center gap-2">
        <AlertCircle className="w-5 h-5 text-orange-600" />
        <h3 className="text-lg font-bold text-purple-800">Update Card Descriptors</h3>
      </div>
      
      <p className="text-sm text-purple-700">
        Regenerate any backstories longer than 60 characters into short one-line superpowers (≤60 chars).
      </p>
      
      <Button 
        onClick={handleUpdateDescriptors}
        disabled={isUpdating}
        className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold px-6 py-3 rounded-full"
      >
        {isUpdating ? (
          <>
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            Updating...
          </>
        ) : (
          'Update All Descriptors'
        )}
      </Button>
      
      {updateStatus && (
        <div className="mt-4 p-3 bg-white rounded-lg border border-purple-200">
          <p className="text-sm font-mono">{updateStatus}</p>
        </div>
      )}
    </div>
  );
};

export default UpdateDescriptors;